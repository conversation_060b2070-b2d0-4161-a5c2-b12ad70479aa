{"_args": [["isarray@0.0.1", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "isarray@0.0.1", "_id": "isarray@0.0.1", "_inBundle": false, "_integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "_location": "/document/express/connect/multiparty/readable-stream/isarray", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "isarray@0.0.1", "name": "isarray", "escapedName": "isarray", "rawSpec": "0.0.1", "saveSpec": null, "fetchSpec": "0.0.1"}, "_requiredBy": ["/document/express/connect/multiparty/readable-stream"], "_resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "_spec": "0.0.1", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "dependencies": {}, "description": "Array#isArray for older browsers", "devDependencies": {"tap": "*"}, "homepage": "https://github.com/juliangruber/isarray", "keywords": ["browser", "isarray", "array"], "license": "MIT", "main": "index.js", "name": "isarray", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "scripts": {"test": "tap test/*.js"}, "version": "0.0.1"}