{"_args": [["debug@2.2.0", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "debug@2.2.0", "_id": "debug@2.2.0", "_inBundle": false, "_integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "_location": "/socket.io-parser/debug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "debug@2.2.0", "name": "debug", "escapedName": "debug", "rawSpec": "2.2.0", "saveSpec": null, "fetchSpec": "2.2.0"}, "_requiredBy": ["/socket.io-parser"], "_resolved": "https://registry.npm.taobao.org/debug/download/debug-2.2.0.tgz?cache=0&sync_timestamp=1607566571506&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.2.0.tgz", "_spec": "2.2.0", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}], "dependencies": {"ms": "0.7.1"}, "description": "small debugging utility", "devDependencies": {"browserify": "9.0.3", "mocha": "*"}, "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./node.js", "name": "debug", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "version": "2.2.0"}