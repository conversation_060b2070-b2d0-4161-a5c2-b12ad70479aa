[2025-07-28 12:45:51.809] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'mjhand' of undefined
    at GameCodeChunTian.sendNewCard (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:662:44)
    at GameCodeChunTian.handleZhuangConfirm (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:2151:14)
    at GameCodeChunTian.baoChun (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:2087:14)
    at Table.baoChun (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:463:24)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:58:15)
[2025-07-28 15:47:09.242] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: undefined
[2025-07-28 15:47:09.243] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:undefined
[2025-07-28 15:47:09.244] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: undefined, msg: {"namespace":"sys","serverType":"pkroom","service":"msgRemote","method":"forwardMessage","args":[{"id":0,"type":1,"compressRoute":0,"route":"pkroom.handler.tableMsg","body":{"enmsg":"C2xc7PjP+A64HfAhQbNRqMCx3q8mQbrwjoetCwPSs3k=","cmd":"MJTick"},"compressGzip":0},{"id":20,"frontendId":"pkcon000","uid":100005,"settings":{"pkplayer":"pkplayer000"}}]}, error code: 2
[2025-07-28 18:20:59.410] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:20:59.411] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:20:59.412] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:20:59.519] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:20:59.520] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:20:59.520] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:08.787] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'Rpc' of undefined
    at E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1245:28
    at global.loadPlayer (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\pkplayerCode.js:39:20)
    at Component.clubCreateRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1216:9)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
    at Connection.emit (events.js:198:13)
[2025-07-28 18:21:10.722] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'Rpc' of undefined
    at E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1245:28
    at global.loadPlayer (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\pkplayerCode.js:39:20)
    at Component.clubCreateRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1216:9)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
    at Connection.emit (events.js:198:13)
[2025-07-28 18:21:11.549] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'Rpc' of undefined
    at E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1245:28
    at global.loadPlayer (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\pkplayerCode.js:39:20)
    at Component.clubCreateRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1216:9)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
    at Connection.emit (events.js:198:13)
[2025-07-28 18:21:12.183] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'Rpc' of undefined
    at E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1245:28
    at global.loadPlayer (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\pkplayerCode.js:39:20)
    at Component.clubCreateRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1216:9)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
    at Connection.emit (events.js:198:13)
[2025-07-28 18:21:12.863] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Cannot read property 'Rpc' of undefined
    at E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1245:28
    at global.loadPlayer (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\pkplayerCode.js:39:20)
    at Component.clubCreateRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkplayer\component\club.js:1216:9)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
    at Connection.emit (events.js:198:13)
[2025-07-28 18:21:19.387] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:19.388] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:19.388] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:19.497] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:19.498] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:19.498] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:39.406] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:39.407] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:39.408] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:39.516] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:39.517] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:39.518] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:59.389] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:59.390] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:59.391] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:21:59.497] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:21:59.498] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:21:59.499] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:19.391] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:19.392] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:19.394] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:19.502] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:19.503] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:19.505] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:39.406] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:39.407] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:39.408] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:39.512] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:39.513] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:39.514] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:59.402] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:59.402] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:59.403] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:22:59.509] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:22:59.510] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:22:59.511] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:19.402] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:19.403] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:19.403] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:19.512] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:19.514] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:19.514] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:39.399] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:39.400] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:39.401] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:39.506] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:39.507] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:39.508] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:59.400] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:59.402] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:59.404] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:23:59.510] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: pkroom000
[2025-07-28 18:23:59.512] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkroom000
[2025-07-28 18:23:59.512] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkroom000, msg: {"namespace":"user","service":"Rpc","method":"isExistRoom","args":[0]}, error code: 2
[2025-07-28 18:26:59.161] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: this.isDoAfterReady is not a function
    at GameCodeChunTian.pkPassCard (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:1026:27)
    at Table.PKPass (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:693:24)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:58:15)
    at Service.beforeFilter (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:65:3)
    at beforeFilter (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:246:8)
[2025-07-29 10:31:57.203] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] server is not online: pkcon000
[2025-07-29 10:31:57.204] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkcon000
[2025-07-29 10:31:57.205] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkcon000, msg: {"namespace":"sys","service":"channelRemote","method":"pushMessage","args":["baoChunResult",{"enmsg":"o8CgcbddVI8ZdBXw2ZPbQ/v/VFXWjkhXPB3uwpOwUVk="},[100001,100003],{"type":"push","userOptions":{},"isPush":true}]}, error code: 2
[2025-07-29 10:31:57.208] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] server is not online: pkcon000
[2025-07-29 10:31:57.208] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkcon000
[2025-07-29 10:31:57.209] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkcon000, msg: {"namespace":"sys","service":"channelRemote","method":"pushMessage","args":["waitPut",{"enmsg":"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"},[100001,100003],{"type":"push","userOptions":{},"isPush":true}]}, error code: 2
[2025-07-29 10:32:00.825] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] server is not online: pkcon000
[2025-07-29 10:32:00.825] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkcon000
[2025-07-29 10:32:00.826] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkcon000, msg: {"namespace":"sys","service":"channelRemote","method":"pushMessage","args":["DelRoom",{"enmsg":"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"},[100001,100003],{"type":"push","userOptions":{},"isPush":true}]}, error code: 2
[2025-07-29 10:32:03.333] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] server is not online: pkcon000
[2025-07-29 10:32:03.334] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkcon000
[2025-07-29 10:32:03.335] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkcon000, msg: {"namespace":"sys","service":"channelRemote","method":"pushMessage","args":["roundEnd",{"enmsg":"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"},[100001,100003],{"type":"push","userOptions":{},"isPush":true}]}, error code: 2
[2025-07-29 10:32:33.466] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] server is not online: pkcon000
[2025-07-29 10:32:33.468] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:pkcon000
[2025-07-29 10:32:33.469] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: pkcon000, msg: {"namespace":"sys","service":"sessionRemote","method":"push","args":[15,"pkclub","pkclub000"]}, error code: 2
[2025-07-29 10:32:36.720] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] unknown server: undefined
[2025-07-29 10:32:36.720] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\mailstation.js: [MailStation] [39m[pomelo-rpc] fail to find remote server:undefined
[2025-07-29 10:32:36.721] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-client\failureProcess.js: [failprocess] [39mrpc failed with error, remote server: undefined, msg: {"namespace":"sys","serverType":"pkclub","service":"msgRemote","method":"forwardMessage","args":[{"id":7,"type":0,"compressRoute":0,"route":"pkclub.handler.createCommonRoom","body":{"enmsg":"+nHnwJmEzihjIIeVrBGnfgdRSnysefOybLo21yY851k="},"compressGzip":0},{"id":15,"frontendId":"pkcon000","uid":100003,"settings":{"pkplayer":"pkplayer000"}}]}, error code: 2
[2025-07-29 14:35:05.897] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.NotifyAll (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3506:27)
    at Table.DelRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:220:18)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:58:15)
    at Service.beforeFilter (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:65:3)
[2025-07-29 14:35:09.650] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at global.cloneDataAndPush (E:\work\server\game-server-huluwa-qcxy\common\lib\GlobalValues.js:364:31)
    at GameCodeChunTian.EndGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:1487:5)
    at Table.RoomEnd (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3533:32)
    at Table.DelRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:196:27)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
[2025-07-29 14:36:24.895] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:26.347] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:27.382] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:29.901] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:30.144] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:32.812] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:32.984] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:36:33.176] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.initSceneData (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3816:62)
    at Table.Reconnect (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3619:41)
    at Component.JoinGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:446:45)
    at Dispatcher.pro.route (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\dispatcher.js:52:10)
    at Acceptor.cb (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\gateway.js:22:16)
    at processMsg (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:138:12)
    at Connection.<anonymous> (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js:62:11)
    at Connection.emit (events.js:198:13)
    at Connection.emitPacket (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\node_modules\mqtt-connection\connection.js:14:8)
[2025-07-29 14:52:13.986] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.NotifyAll (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3506:27)
    at Table.DelRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:220:18)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:58:15)
    at Service.beforeFilter (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:65:3)
[2025-07-29 14:52:17.143] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at global.cloneDataAndPush (E:\work\server\game-server-huluwa-qcxy\common\lib\GlobalValues.js:364:31)
    at GameCodeChunTian.EndGame (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\poker\GameCodeChunTian.js:1487:5)
    at Table.RoomEnd (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3533:32)
    at Table.DelRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:196:27)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
[2025-07-29 14:53:18.831] [ERROR] pomelo-rpc - [31m\work\server\game-server-huluwa-qcxy\node_modules\pomelo-rpc\lib\rpc-server\acceptors\mqtt-acceptor.js: [mqtt-acceptor] [39mprocess rpc message error TypeError: Converting circular structure to JSON
    at JSON.stringify (<anonymous>)
    at Table.NotifyAll (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Table.js:3506:27)
    at Table.DelRoom (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\games\Protocol.js:220:18)
    at Component.tableMsg (E:\work\server\game-server-huluwa-qcxy\app\servers\pkroom\pkroomCode.js:551:28)
    at Service.handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\handlerService.js:65:20)
    at handle (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\server\server.js:379:25)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:50:7)
    at Object.before (E:\work\server\game-server-huluwa-qcxy\app.js:281:13)
    at next (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:58:15)
    at Service.beforeFilter (E:\work\server\game-server-huluwa-qcxy\node_modules\pomelo\lib\common\service\filterService.js:65:3)
