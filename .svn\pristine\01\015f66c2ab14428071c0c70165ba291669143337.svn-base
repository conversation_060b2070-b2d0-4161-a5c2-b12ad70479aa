// 崇阳麻将
// putType 0出牌  1吃  2抢杠胡  4杠
// eatFlag 胡8 杠4 碰2 吃1

var flowerCards = [];

function GameCodeChongYangMJ(majiang, app) {
    this.majiang = majiang;
    this.app = app;
};

// 游戏建房选项
GameCodeChongYangMJ.prototype.initAreaSelectMode = function(tb) {
    tb.tData.areaSelectMode["maxPlayer"]        = tb.createParams.maxPlayer;
    tb.tData.areaSelectMode["convertible"]      = tb.createParams.convertible;          // 自由人数
    tb.tData.areaSelectMode["guozhuang"]        = tb.createParams.guozhuang;            // 过庄
    tb.tData.areaSelectMode["difen"]            = tb.createParams.difen || 1;           // 底分
    tb.tData.areaSelectMode["isOpenTingTip"]    = tb.createParams.isOpenTingTip;        // 听牌提示

    tb.tData.gameCnName = "崇阳麻将";
};

//测试提交代码
GameCodeChongYangMJ.prototype.initPlayerOnce = function(pl) {
    pl.dir = -1;
    pl.jiazhuNum = -1;
};

GameCodeChongYangMJ.prototype.initPlayer = function (pl) {
    pl.putCount = 0;                        // 出牌数
    pl.skipHu = [];                         // 过胡
    pl.gangType = 0;                        // 玩家杠的类型 1:明杠 2:补杠 3暗杠
    pl.trust = false;
    pl.tPutCard = false;
    pl.eatCard = 0;                         // 吃牌的次数
};

GameCodeChongYangMJ.prototype.collectPlayer = function(tb) {
    return tb.collectPlayer(
        'trust',
        'eatFlag',
        'eatFlag2',         // 长沙麻将同时出两个牌时专用，
        'eatFlag3',
        'eatFlag4',
        'handCount',
        'putCount',
        'zuiUid',
        'zuiCount',
        'jiaPai',
        'qingDui',
        'info',
        'mjState',
        'firstFlower8', //如皋麻将胡，是否起手8花
        'mjpeng',
        'mjgang0',
        'mjgang1',
        'mjTeshuGang0',
        'mjTeshuGang1',
        'mjchi',
        'mjchiCard',
        'mjput',
        'onLine',
        'huType',
        'skipHu',
        'skipPeng',
        'skipLong',
        'isQiHu',
        'delRoom',
        'delRoomHePai', //如皋长牌的和牌
        'isNew',
        'winall',
        'linkZhuang',
        'pengchigang',
        'isTing',
        'mjflower',                     //花牌
        'newSendCard',                  //新发的牌,听牌使用
        'roomStatistics',
        'zimoTotal',
        'dianpaoTotal',
        'minggangTotal',
        'angangTotal',
        'putCardAfterTing',             //听牌之后出的那张牌
        'tingIndex',                    //听牌出牌的位置
        'jiazhuNum',                     //加注
        'jiachuiNum',                    // 加锤
        'mjwei',                        //偎牌
        'mjsort',                        //提、偎、跑、碰、吃的顺序
        'wangType',
        'wangStatus',
        'long',
        'locationMsg',                  // 位置信息
        'locationApps',                 // 定位修改App
        'rate',                          // 倍率
        'qiShouHu',
        'haiDiLaoState',                 // 长沙麻将 海底捞
        'isTianting',                    // 涟水麻将，天听
        'tableMsg',
        'jiaoFen',              // 三打哈叫分
        'isPaiFen',            // 三打哈拍分
        'isAgreeTouXiang',  // 三打哈投降选择
        'limitHuPutCard',  // 耒阳字牌打后限胡的牌
        'limitHuTypeList',  // 耒阳字牌限胡类型
        'canNotPutCard',  // 耒阳字牌不能打的牌
        'canGangHand',   // 耒阳麻将 能否杠手牌标志
        'gangFourCounts',
        'mjhandFour',
        'qiang',
        'mustJiao',
        'dir',
        'lastOffLineTime',
        'freeBegin',       // 自由人数投票数据
        'touzi',            //新宁麻将
        'winone',
        'jiepaoTotal',       //接炮次数
        'tPutCard'
    );
};

// 随机癞子牌
GameCodeChongYangMJ.prototype.randomHunCard = function (cards, tData) {
    var autoCard = cards[Math.floor(Math.random() * cards.length)];
    var hunCard = null;

    var nCycleCount = 10;
    if (autoCard >= 1 && autoCard <= 29) { // 条 | 万 | 筒
        var bByte = (autoCard + 1) % nCycleCount;
        var bInteger = Math.floor(autoCard / nCycleCount);
        hunCard = (bByte > 0 ? (bInteger * 10 + bByte) : (bInteger * 10 + 1));
    }
    else if (autoCard == 91) { // 白板
        hunCard = 31;
    }
    else { // 东南西北中发
        var bInteger = Math.floor(autoCard / nCycleCount);
        hunCard = (bInteger + 1) * 10 + 1;
    }
    
    return hunCard;
};

GameCodeChongYangMJ.prototype.freePersonZhuang = function(tb){
    var tData = tb.tData;
    tData.zhuang = -1;
    this.countZhuang(tData, tb);
};

// 发手牌前的处理
GameCodeChongYangMJ.prototype.doBeforeHands = function(tb) {
    var tData = tb.tData;
    tData.huangNum = 20;    // 记录荒庄剩余牌数量
};

// 发手牌后的处理
GameCodeChongYangMJ.prototype.doAfterHands = function (tb) {
    var tData = tb.tData;
};

// 发牌
GameCodeChongYangMJ.prototype.sendNewCard = function (tb) {
    var tData = tb.tData;
    var cards = tb.cards;
    var g_this = this;
    if (tb.AllPlayerCheck(function (pl) {
                return pl.mjState == TableState.waitCard;
            }
        )
    ) {
        // 荒庄留的牌的数量
        logger.debug("sendNewCard=====TableState.waitCard=====");
        var huangNum = tData.huangNum;
        var totalCar = this.getCardTotalNum(tData);
        logger.debug("sendNewCard=====总牌数:" + totalCar);
        logger.debug("sendNewCard=====huangNum:" + huangNum);
        logger.debug("sendNewCard=====剩余牌:" + (totalCar - huangNum - tData.cardNext));

        // 记录杠的个数
        var gangTotalNum = 0;
        tb.AllPlayerRun(function (p) {
            gangTotalNum += (p.mjgang0.length + p.mjgang1.length);
        });

        if (gangTotalNum < 3 && ((tData.cardNext < totalCar - huangNum) || (tData.putType > 1 && tData.putType < 4 && tData.cardNext < totalCar))) {
            var newCard = cards[tData.cardNext++];
            if (tData.putType == 0 || tData.putType == 4) {
                tData.curPlayer = (tData.curPlayer + 1) % tData.maxPlayer;
            }
            var uid = tData.uids[tData.curPlayer];
            var pl = tb.getPlayer(uid);
            pl.mjhand.push(newCard);
            pl.newSendCard = newCard;
            pl.isNew = true;
            pl.skipHu = [];
            pl.gangType = 0;
            pl.skipPeng = [];
            tData.tState = TableState.waitPut;// 发牌了,那么裁判状态为等待出牌
            tb.AllPlayerRun(function (p) {
                p.mjState = TableState.waitPut;
                p.eatFlag = 0;
            });
            if (this.getHuType(pl, 0, tData)) {
                pl.eatFlag = 8;
            }

            pl.notify("newCard", {newCard: newCard, eatFlag: pl.eatFlag});
            cloneDataAndPush(tb.mjlog, "logNewCard", {newCard: newCard, eatFlag: pl.eatFlag}, {uid: pl.uid});//发牌
            tb.NotifyAll("waitPut", tData);
            cloneDataAndPush(tb.mjlog, "waitPut", tData);
            tb.doTrust(pl);
            //托管 > 自动摸打
            if (!pl.trust  && pl.tPutCard && pl.eatFlag != 8
                && this.majiang.canGang1(pl.mjpeng, pl.mjhand, pl.isTing, pl.newSendCard, tData).length == 0) {
                // 听牌后自动出牌
                if (pl.autoTimer) {
                    clearTimeout(pl.autoTimer);
                }
                pl.autoTimer = setTimeout(function() {
                    pl.autoTimer = null;
                    if (pl.mjState == TableState.waitPut) {
                        g_this.mjPutCard(pl, {cmd: "MJPut", card: pl.mjhand[pl.mjhand.length - 1]}, null, null, tb);
                    }
                }, 1000 * 0.5);
            }

            return true;
        }
        else {
            //没有牌了
            logger.debug("sendNewCard=====没牌,黄庄 endGame=====");
            this.EndGame(tb, null);
        }
    }
    else {
        logger.debug("sendNewCard=====玩家状态问题，不能发牌");

    }
    return false;
};

GameCodeChongYangMJ.prototype.isHardFlower = function (card) {
    return flowerCards.indexOf(card) >= 0;
}

// 获取总的牌的数量
GameCodeChongYangMJ.prototype.getCardTotalNum = function (tb) {
    return 136;
}

// 测试代码
GameCodeChongYangMJ.prototype.getTestCards = function (tb) {
    return 100;
}

// 获取胡的类型, 返回true还是false
GameCodeChongYangMJ.prototype.getHuType = function (pl, cd, tData) {
    // 四癞
    var hunCount = this.majiang.getHunCount(cd ? pl.mjhand.concat(cd) : pl.mjhand, tData);
    if (hunCount == 4) {
        return true;
    }

    var canHu = this.majiang.canHuByHunCard(pl, cd, tData);
    logger.debug("getHuType=====gpl.uid:" + pl.uid + ",getHuType huState:" + canHu);

    // 小胡需要2，5，8做将
    if (canHu && this.majiang.isPingHu(pl, cd, tData)) {
        logger.debug("getHuType======================= : 小胡");
        // 癞子牌统一替换为 200
        var cards = this.majiang.transformCards(cd ? pl.mjhand.concat(cd) : pl.mjhand.slice(), tData.hunCard);
        logger.debug("getHuType======================= : cards = ", cards);
        return this.majiang.canHuLaizi(cards, 0, true);
    }

    return canHu;
};

// 获取状态
GameCodeChongYangMJ.prototype.getEatFlag = function (pl, tb) {
    var tData = tb.tData;
    var cd = tData.lastPutCard;
    var leftCard = this.getCardTotalNum(tb) - tData.cardNext - tData.huangNum;
    var eatFlag = 0;
    // if (cd != tData.hunCard) { // 非百搭牌
        
        if (this.getHuType(pl, cd, tData)) { // 点炮
            eatFlag += 8;
            // 过胡
            if (tData.areaSelectMode["guozhuang"]) {
                if (pl.skipHu.indexOf(cd) >= 0) {
                    eatFlag -= 8;
                }
                else if (pl.skipHu.length > 0) {
                    var skipHuScoreMax = 0;
                    var canHuScore = this.huScoreNumber(tb, pl, cd);
                    // 获取最大的过胡分数
                    for (var i = 0; i < pl.skipHu.length; i++) {
                        var score = this.huScoreNumber(tb, pl, pl.skipHu[i]);
                        skipHuScoreMax = score > skipHuScoreMax ? score : skipHuScoreMax;
                    }
                    logger.debug("===== canHuScore ===== " , canHuScore);
                    logger.debug("===== skipHuScoreMax ===== " , skipHuScoreMax);
                    // 胡分小于过胡分，则不可胡
                    if (canHuScore <= skipHuScoreMax) {
                        eatFlag -= 8;
                    }
                }
            }
        }

        if (leftCard > 0 && this.majiang.canGang0(pl.mjhand, cd, pl.isTing, tData.hunCard)) { // 明杠
            eatFlag += 4;
        }

        if (leftCard > 0 && this.majiang.canPeng(pl.mjhand, cd)) { // 碰
            eatFlag += 2;
        }
        if (leftCard > 0 && (tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] == pl.uid) && this.majiang.canChi(pl.mjhand, cd).length > 0) { // 吃
            eatFlag += 1;
        }
    // }
    return eatFlag;
}

// 自动摸打信息
GameCodeChongYangMJ.prototype.mjTouchPutCard = function (pl, msg, session, next, tb) {
    if (pl.autoTimer) {
        clearTimeout(pl.autoTimer);
        pl.autoTimer = null;
    }
    
    var tData = tb.tData;
    pl.tPutCard = msg.tPutCard;
    msg = {tPutCard : pl.tPutCard, uid : pl.uid};
    pl.notify("MJTouchPutCard", msg);
    cloneDataAndPush(tb.mjlog, "MJTouchPutCard", msg);
}

// 出
GameCodeChongYangMJ.prototype.mjPutCard = function (pl, msg, session, next, tb) {
    if (pl.isTing && msg.card != pl.newSendCard) {
        logger.debug("mjPutCard=====听牌后只能打抓的牌=" + pl.newSendCard + "你打的是=" + msg.card);
        return;
    }
    var tData = tb.tData;
    if (tData.tState == TableState.waitPut && pl.uid == tData.uids[tData.curPlayer]) {
        var cdIdx = pl.mjhand.indexOf(msg.card);
        //正常逻辑
        if (cdIdx >= 0) {
            tb.AllPlayerRun(function (pll) {
                pll.putType = 1;// 玩家出牌状态为普通
            });
            pl.mjhand.splice(cdIdx, 1);
            msg.uid = pl.uid;
            tData.lastPutCard = msg.card;
            tData.putType = 0;
            tData.lastPutPlayer = tData.curPlayer;
            tData.tState = TableState.waitEat;
            pl.mjState = TableState.waitCard;
            
            if (pl.eatFlag >= 8) {
                pl.skipHu.push(tData.lastPutCard);
            }
            pl.skipPeng = [];

            pl.eatFlag = 0;//自己不能吃
            var eatFlags = {};
            eatFlags[pl.uid] = 0;
            var t_this = this;
            pl.putCount++;
            // var isFlower = msg.card == tData.hunCard ? true: false;//判断是否补张
            var isFlower = false;
            if (isFlower) {
                pl.mjput.push(msg.card);
                logger.debug("mjPutCard=====通知客户端补花了=======");
                pl.mjflower.push(msg.card);
                pl.putType = 5;
                tData.putType = 5;//花牌

                tb.AllPlayerRun(function (pl) {
                    pl.mjState = TableState.waitCard;
                });

                var tmp={};
                tmp.uid = pl.uid;
                tmp.card = msg.card;
                tmp.putType = tData.putType;
                tmp.skipHu = pl.skipHu;
                tmp.skipPeng = pl.skipPeng;
                tb.NotifyAll("MJPut", tmp);
                cloneDataAndPush(tb.mjlog, "MJPut",tmp);

            }
            else {
                pl.mjput.push(msg.card);
                tb.AllPlayerRun(function (p) {
                    if (p != pl) {
                        //发送手牌到别人
                        p.eatFlag = t_this.getEatFlag(p, tb);
                        eatFlags[p.uid] = p.eatFlag;
                        if (p.eatFlag != 0) {
                            p.mjState = TableState.waitEat;
                            tb.doTrust(p);
                        }
                        else {
                            p.mjState = TableState.waitCard;
                        }
                    }
                    else {
                        //自己补张
                        p.mjState = TableState.waitCard;
                    }
                });
                var cmd = msg.cmd;
                delete msg.cmd;
                logger.debug("mjPutCard=====玩家出牌了======");
                msg.putType = tData.putType;
                msg.skipHu = pl.skipHu;
                msg.skipPeng = pl.skipPeng;
                msg.eatFlags = eatFlags;
                logger.debug("mjPutCard=====cmd=====" + cmd + "     msg:" + JSON.stringify(msg));
                tb.NotifyAll(cmd, msg);
                cloneDataAndPush(tb.mjlog, cmd, msg);
            }
            logger.debug("mjPutCard=====xcl test");
            tData.saveActions = [];
            this.sendNewCard(tb);
        }
    }
}

GameCodeChongYangMJ.prototype.checkFinishRound = function (tb, pl) { // 判断过吃碰杠操作是否继续
    logger.debug("mjPutCard=====checkFinishRound");
    if (tb.CheckPlayerCount(function (p) {
            return p.mjState == TableState.roundFinish;
        }) > 0) {// 存在已经胡的玩家
        if (this.highPlayerHu(tb, pl)) { // 还有玩家未操作
            pl.mjState = TableState.roundFinish;
            pl.notify("MJPass", {mjState: pl.mjState, skipHu: pl.skipHu});
            logger.debug("mjPutCard=====checkFinishRound  还有玩家未操作");
        }
        else {
            this.EndGame(tb, pl); // 一炮多响情况下，最后操作的那个人不胡，“过吃碰杠”也应结束游戏
            logger.debug("mjPutCard=====checkFinishRound  游戏结束");
        }
        return true;
    }
    return false;
}

// 过
GameCodeChongYangMJ.prototype.mjPassCard = function (pl, msg, session, next, tb) {
    //logger.debug("======================GameCodeHZMJ.prototype.mjPassCard======================");
    var tData = tb.tData;
    logger.debug("mjPassCard========tData.tState:" + tData.tState);
    logger.debug("mjPassCard========pl.mjState:" + pl.mjState);
    if(msg.cardNext && msg.cardNext != tData.cardNext){
        return;
    }
    if (tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut) {
        cloneDataAndPush(tb.mjlog, "MJPass", {mjState: pl.mjState}, {uid: pl.uid});
    }
    else if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat) {
        if (this.checkFinishRound(tb, pl))
            return;
        if (pl.eatFlag == msg.eatFlag) {
            pl.mjState = TableState.waitCard;
            if (pl.eatFlag >= 8) { // 过胡 
                pl.skipHu.push(tData.lastPutCard);
            }
            if (pl.eatFlag & 2) {
                // pl.skipPeng.push(tData.lastPutCard);
            }
            pl.eatFlag = 0;
            var newMsg = {};
            newMsg.mjState = pl.mjState;
            newMsg.skipHu = pl.skipHu;
            newMsg.skipPeng = pl.skipPeng;
            if(!pl.trust  && pl.tPutCard){
                newMsg.touchCard = pl.mjhand[pl.mjhand.length -1];
            }
            pl.notify("MJPass", newMsg);
            cloneDataAndPush(tb.mjlog, "MJPass", newMsg, {uid: pl.uid});

            // pl.notify("MJPass", {mjState: pl.mjState, skipHu: pl.skipHu, skipPeng: pl.skipPeng});
            // cloneDataAndPush(tb.mjlog, "MJPass", {mjState: pl.mjState, skipHu: pl.skipHu, skipPeng: pl.skipPeng}, {uid: pl.uid});
            tData.saveActions.sort(function (a, b) {return b.eatFlag - a.eatFlag;});
            for (var i = 0; i < tData.saveActions.length; i++) {
                var action = tData.saveActions[i];
                if (action.eatFlag <= msg.eatFlag) {
                    tData.saveActions = [];
                    action.actionFunc();
                    break;
                }
            }
            this.sendNewCard(tb);
        }
    }
    else if (tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish || tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady) {
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', {uid: pl.uid, onLine: true, mjState: pl.mjState});
        pl.eatFlag = 0;
        tb.AllPlayerRun(function(p) {
            p.jiazhuNum = -1;
        });

        tb.startGame();

        // if (tb.PlayerCount() == tData.maxPlayer && tb.AllPlayerCheck(function (p) {
        //     return p.mjState == TableState.isReady;
        // })) {
        //     if(tData.tState == TableState.roundFinish){
        //         this.countZhuang(tData, tb);
        //     }
        //     tb.runStartGame();
        // }
    }
};

// 吃
GameCodeChongYangMJ.prototype.mjChiCard = function (pl, msg, session, next, tb) {
    logger.debug("======================mjChiCard======================");
    if (pl.isTing) {
        logger.debug("mjChiCard============pl.isTing()===不能吃牌=======" + pl.isTing);
        return;
    }
    var tData = tb.tData;
    logger.debug("mjChiCard=====tData.tState=====" + tData.tState);
    logger.debug("mjChiCard=====pl.mjState========" + pl.mjState);
    logger.debug("mjChiCard=====tData.uids[tData.curPlayer]======" + tData.uids[tData.curPlayer]);
    logger.debug("mjChiCard=====pl.uid===========" + pl.uid);
    logger.debug("mjChiCard=====tData.uids[(tData.curPlayer+1)%tData.maxPlayer]=========" + tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]);
    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid &&
        tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] == pl.uid //下家限制
    ) {
        if (this.checkFinishRound(tb, pl))
            return;
        logger.debug("==================mjChiCard====================");
        //此处必须保证没有其他玩家想 胡牌 碰牌 杠牌
        if (tb.AllPlayerCheck(function(p) {
                if (p == pl) {
                    return true;
                }
                return p.eatFlag == 0;
            })) {
            var cd0 = tData.lastPutCard;
            var cd1 = tData.lastPutCard;
            if (msg.pos == 0) {
                cd0 += 1;
                cd1 += 2;
            }
            else if (msg.pos == 1) {
                cd0 -= 1;
                cd1 += 1;
            }

            else {
                cd0 -= 2;
                cd1 -= 1;
            }

            var hand = pl.mjhand;
            var idx0 = hand.indexOf(cd0);
            var idx1 = hand.indexOf(cd1);
            if (idx0 >= 0 && idx1 >= 0) {
                logger.debug("mjChiCard=====idx0=====" + idx0);
                // 如果手里有这两张牌
                hand.splice(idx0, 1);
                idx1 = hand.indexOf(cd1);
                hand.splice(idx1, 1);
                var eatCards = [cd0, cd1, tData.lastPutCard];
                eatCards.sort(function(a, b) {
                    return a - b;
                });
                logger.debug("mjChiCard=====hand=====" + JSON.stringify(hand));
                logger.debug("mjChiCard=====eatCards=====" + JSON.stringify(eatCards));

                pl.mjchi = pl.mjchi.concat(eatCards);
                pl.mjchiCard.push(tData.lastPutCard);
                pl.isNew = false;

                pl.pengchigang["chi"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});

                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;
                logger.debug("mjChiCard=====putCardPl.mjput=====" + JSON.stringify(putCardPl.mjput));
                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tData.tState = TableState.waitPut;

                tb.AllPlayerRun(function(p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });

                //吃碰杠
                msg.cpginfo =
                    {
                        id: pl.uid,
                        pengchigang: pl.pengchigang
                    };

                var chiMsg =
                    {
                        mjchi: eatCards,
                        mjchiCard: pl.mjchiCard,
                        tData: JSON.parse(JSON.stringify(tData)),
                        pos: msg.pos,
                        from: lastPlayer,
                        eatFlag: msg.eatFlag,
                        cpginfo: msg.cpginfo
                    };
                logger.debug("mjChiCard=====chiMsg=====" + JSON.stringify(chiMsg));

                // 第三句，要记录被吃牌的人，用于清一色包分
                pl.eatCard++;
                if (pl.eatCard == 3) {
                    pl.eatInfo = {pos: tData.lastPutPlayer, card: tData.lastPutCard};
                }

                tb.NotifyAll('MJChi', chiMsg);
                cloneDataAndPush(tb.mjlog, "MJChi", chiMsg);
                tb.doTrust(pl);
            }
        }
        else {
            var _this = this;
            var actionFunc = (function() {
                return function() {
                    _this.mjChiCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({eatFlag: 1, actionFunc: actionFunc});
            logger.debug("mjChiCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", {uids: [pl.uid]});
        }
    }
};


// 碰
GameCodeChongYangMJ.prototype.mjPengCard = function (pl, msg, session, next, tb) {

    var tData = tb.tData;
    // if (tData.hunCard == tData.lastPutCard) { return ; }
    if (
        tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid
        // && pl.skipPeng.indexOf(tData.lastPutCard) < 0 // 过碰的牌
    ) {
        if (this.checkFinishRound(tb, pl))
            return;
        //此处必须保证没有其他玩家想胡牌
        if (tb.AllPlayerCheck(function (p) {
                if (p == pl) {
                    return true;
                }
                return p.eatFlag < 8;
            })) {
            var hand = pl.mjhand;
            var matchnum = 0;
            for (var i = 0; i < hand.length; i++) {
                if (hand[i] == tData.lastPutCard) {
                    matchnum++;
                }
            }
            if (matchnum >= 2) {
                // 碰
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                pl.mjpeng.push(tData.lastPutCard);
                pl.pengchigang["peng"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});
                pl.openDoorState = true;
                if (matchnum == 3) {
                    pl.mjpeng4.push(tData.lastPutCard);
                }
                pl.isNew = false;
                pl.tPutCard = false;
                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;

                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tb.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });
                tData.tState = TableState.waitPut;
                //吃碰杠
                msg.cpginfo =
                    {
                        id: pl.uid,
                        openDoorState: pl.openDoorState,
                        pengchigang: pl.pengchigang
                    };
                tb.NotifyAll('MJPeng',
                    {
                        tData: tData,
                        from: lastPlayer,
                        cpginfo: msg.cpginfo
                    });
                cloneDataAndPush(tb.mjlog, 'MJPeng', {
                    tData: tData,
                    from: lastPlayer,
                    eatFlag: msg.eatFlag,
                    cpginfo: msg.cpginfo
                });//碰
                tb.doTrust(pl);

                // 第三句，要记录被吃牌的人，用于清一色包分
                pl.eatCard++;
                if (pl.eatCard == 3) {
                    pl.eatInfo = {pos: tData.lastPutPlayer, card: tData.lastPutCard};
                }
            }
        }
        else {
            var _this = this;
            var actionFunc = (function () {
                return function() {
                    _this.mjPengCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({eatFlag: 2, actionFunc: actionFunc});
            logger.debug("mjPengCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", {uids: [pl.uid]});
        }
    }
}

// 杠
GameCodeChongYangMJ.prototype.mjGangCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid && this.checkFinishRound(tb, pl))
        return;
    if (
        (
            //吃牌杠
            tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid
            //此处必须保证没有其他玩家想胡牌
            && (
                tb.AllPlayerCheck(function (p) {
                    if (p == pl) return true;
                    return p.eatFlag < 8;
                })
            )
            //自摸牌杠
            || tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut && tData.uids[tData.curPlayer] == pl.uid
        )
    ) {
        var hand = pl.mjhand;
        var handNum = 0;
        var putCardPl = null;
        for (var i = 0; i < hand.length; i++) {
            if (hand[i] == msg.card) {
                handNum++;
            }
        }
        if (tData.tState == TableState.waitEat && handNum == 3 && tData.lastPutCard == msg.card) {
            // 自己有三张，杠别人的
            putCardPl = tb.getPlayer(tData.uids[tData.curPlayer]);
            var mjput = putCardPl.mjput;
            if (mjput.length > 0 && mjput[mjput.length - 1] == msg.card) {
                mjput.length = mjput.length - 1;
            }
            else {

                return;
            }
            //记录点杠
            pl.mjgang0.push(msg.card);//吃明杠
            //结算界面明杠数+1
            pl.minggangTotal += 1;

            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);

            msg.gang = 1;
            pl.gangType = 1;
            msg.from = tData.curPlayer;
            pl.isNew = false;
            pl.pengchigang["gang"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});

            // 第三句，要记录被吃牌的人，用于清一色包分
            pl.eatCard++;
            if (pl.eatCard == 3) {
                pl.eatInfo = {pos: tData.lastPutPlayer, card: tData.lastPutCard};
            }
        }
        else if (tData.tState == TableState.waitPut && handNum == 4) {
            // 暗杠
            pl.mjgang1.push(msg.card);
            //结算界面暗杠数+1
            pl.angangTotal += 1;
            
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);

            msg.gang = 3;
            pl.gangType = 3;
        }
        else if (tData.tState == TableState.waitPut && handNum == 1 && pl.mjpeng.indexOf(msg.card) >= 0) {
            //自摸明杠
            //找出点碰的人
            var pengPos = tData.lastPutPlayer;
            var pengList = pl.pengchigang["peng"];
            for (var t = 0; t < pengList.length; t++) {
                var item = pengList[t];
                if (item.card == msg.card) {
                    pengPos = item.pos;
                    pengList.splice(t, 1);
                }
            }
            pl.pengchigang["pgang"].push({pos: pengPos, card: msg.card});
            pl.mjgang0.push(msg.card);
            //结算界面明杠数+1
            pl.minggangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            pl.mjpeng.splice(pl.mjpeng.indexOf(msg.card), 1);
            msg.gang = 2;
            pl.gangType = 2;
        }
        else {

            return;
        }
        msg.uid = pl.uid;
        //判断是否有抢杠胡
        var canRobGangHu = 0;
        var canQiangGang = true;

        pl.putType = 4;
        pl.tPutCard = false;
        var t_this = this;
        var eatFlags = {};
        for (var i = 0; i < tData.maxPlayer; i++) {
            var p = tb.players[tData.uids[(tData.curPlayer + i) % tData.maxPlayer]];
            // 抢杠
            p.mjState = TableState.waitCard;
            p.eatFlag = 0;
            if (msg.gang == 2 && p != pl && canQiangGang) {
                // 只有在自摸明杠的时候才能抢杠胡
                var huState = t_this.getHuType(p, msg.card, tData);
                if (huState && (canQiangGang)) { // 抢杠胡
                    canRobGangHu++;
                    p.mjState = TableState.waitEat;
                    p.eatFlag = 8;
                    tb.doTrust(p);
                }
            }
            eatFlags[p.uid] = p.eatFlag;
        };

        tData.tState = TableState.waitEat;
        if (canRobGangHu > 0) {
            // 抢杠胡
            tData.putType = 2;
            tData.curPlayer = tData.uids.indexOf(pl.uid);
            tData.lastPutCard = msg.card;
        }
        else {
            tData.putType = 0;
            tData.curPlayer = (tData.uids.indexOf(pl.uid) + (tData.maxPlayer - 1)) % tData.maxPlayer;
        }
        //吃碰杠
        msg.cpginfo =
            {
                id: pl.uid,
                pengchigang: pl.pengchigang,
                openDoorState: pl.openDoorState
            };
        msg.eatFlags = eatFlags;
        //杠后当前状态为杠
        tb.NotifyAll('MJGang', msg);
        cloneDataAndPush(tb.mjlog, 'MJGang', msg);
        logger.debug("mjGangCard=====通知客户端扛了=====");

        // 杠后荒庄留牌减2
        tData.huangNum -= 2;

        if (tData.putType != 2) {
            // 不被别人抢杠，才会发牌
            this.sendNewCard(tb);
        }
    }
    else {
        var _this = this;
        var actionFunc = (function () {
            return function() {
                _this.mjGangCard(pl, msg, session, next, tb);
            }
        })();
        tData.saveActions.push({eatFlag: 4, actionFunc: actionFunc});
        tb.NotifyAll("loadOther", {uids: [pl.uid]});
    }
}

GameCodeChongYangMJ.prototype.highPlayerHu = function (tb, pl) {
    var g_this = this;
    if (tb.CheckPlayerCount(function (p) {
            logger.debug("highPlayerHu=====p.mjState:" + p.mjState + ", eatFlag = " + p.eatFlag);
            if (pl.uid != p.uid && p.mjState == TableState.waitEat && p.eatFlag >= 8) {
                return true;
            }
            return false;
        }) > 0) {
        logger.debug("highPlayerHu=====HighPlayerHu: return true=====");
        return true;
    }
    logger.debug("highPlayerHu=====HighPlayerHu: return false=====");
    return false;
}

GameCodeChongYangMJ.prototype.mjJieHu = function (pl, tb) {
    var tData = tb.tData;
    if (pl == tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]]) {

    }
    else if (pl == tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8) {
            return true;
        }
    }
    else if (pl == tb.players[tData.uids[(tData.curPlayer + 3) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        var eatFlag2 = tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8 || eatFlag2 >= pl.eatFlag || eatFlag2 >= 8) {
            return true;
        }
    }
    return false;
}

// 胡
GameCodeChongYangMJ.prototype.mjHuCard = function (pl, msg, session, next, tb) {
    logger.debug("======================mjHuCard======================");
    var tData = tb.tData;
    var canEnd = false;
    var isYingMo = false;
    //自摸胡
    if (
        tData.tState == TableState.waitPut &&
        pl.mjState == TableState.waitPut &&
        pl.isNew &&
        tData.uids[tData.curPlayer] == pl.uid &&
        pl.eatFlag >= 8
    )
    {
        logger.debug("mjHuCard=====自摸=====");
        pl.winType = WinType.pickNormal;    //自摸
        //结算自摸胡加1
        pl.zimoTotal += 1;
        canEnd = true;
        tData.lastPutCard = pl.newSendCard;
        isYingMo = this.calYingMo(tb, pl);
        pl.isYingMo = isYingMo;
    }
    else if (
        tData.tState == TableState.waitEat &&
        pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid &&
        pl.eatFlag >= 8
    ) {
        if (tData.tState == TableState.waitEat) {
            logger.debug("mjHuCard=====点炮胡=====");
            //截胡
            var duoHu = false;
            if (!duoHu && this.mjJieHu(pl, tb)) {
                logger.debug("mjHuCard=====有截胡玩家，请等待=======");
                logger.debug("mjHuCard=====通知客户端等待其他玩家操作");
                logger.debug("mjHuCard=====已通知玩家等待uid:" + pl.uid);
                tb.NotifyAll("loadOther", {uids: [pl.uid]});
                return;
            }
            var winType = null;
            if (tData.putType == 0 || tData.putType == 4) //点炮
            {
                winType = WinType.eatPut;
                pl.dianPaoPlayer = tData.lastPutPlayer;
                //结算界面点炮总数+1
                var dianPaoPl = tb.getPlayer(tData.uids[tData.lastPutPlayer]);
                dianPaoPl.dianpaoTotal += 1;
            }
            else //抢杠胡tData.putType == 2
            {
                //抢杠胡
                winType = WinType.eatGang;
                pl.dianPaoPlayer = tData.curPlayer;
                //结算界面点炮总数+1
                var robGangPl = tb.getPlayer(tData.uids[tData.curPlayer]);
                robGangPl.dianpaoTotal += 1;
                logger.debug("mjHuCard=====删除杠牌" + tData.lastPutCard);
                tb.AllPlayerRun(function (p) {
                    var pgangList = p.pengchigang["pgang"];
                    logger.debug("mjHuCard=====" + p.uid, pgangList);
                    for (var i = 0; i < pgangList.length; i++) {
                        if (pgangList[i].card == tData.lastPutCard) {
                            logger.debug("mjHuCard=====splice");
                            p.pengchigang["peng"].push(pgangList[i]);
                            pgangList.splice(i, 1);
                        }
                    }
                    var gangIndex = p.mjgang0.indexOf(tData.lastPutCard);
                    if (gangIndex >= 0) {
                        logger.debug("mjHuCard=====mjgang0");
                        p.mjpeng.push(tData.lastPutCard)
                        p.mjgang0.splice(gangIndex, 1);
                    }
                });
                logger.debug("mjHuCard=====删除杠牌 end");
            }
            logger.debug("mjHuCard=====pl.uid:" + pl.uid);
            pl.mjhand.push(tData.lastPutCard);
            pl.winType = winType;
            //结算界面接炮总数+1
            pl.jiepaoTotal += 1;

            isYingMo = this.calYingMo(tb, pl);
            pl.isYingMo = isYingMo;

            logger.debug("mjHuCard=====pl.mjState==TableState.waitEat=====" + (pl.mjState == TableState.waitEat));
            if (duoHu && pl.mjState == TableState.waitEat && this.highPlayerHu(tb, pl)) {
                logger.debug("mjHuCard=====一炮多胡=====");
                logger.debug("mjHuCard=====roundFinish: pl.uid=====" + pl.uid);
                tb.NotifyAll("loadOther", {uids: [pl.uid]});
                pl.mjState = TableState.roundFinish;
                var huMsg = {
                    uid: pl.uid, 
                    eatFlag: msg.eatFlag, 
                    huWord: this.majiang.showHuWord(tData, pl),
                    mjhand: pl.mjhand,
                    isYingMo : isYingMo
                };
                tb.NotifyAll("MJHu", huMsg);
                cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
            }
            else {
                logger.debug("mjHuCard=====截胡=====");
                canEnd = true;
            }
        }
    }
    
    if (canEnd) {
        logger.debug("mjHuCard=====canEnd=====");
        var huMsg = {
            uid: pl.uid, 
            eatFlag: msg.eatFlag, 
            huWord: this.majiang.showHuWord(tData, pl),
            mjhand: pl.mjhand,
            isYingMo : isYingMo
        };
        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
    }
};

GameCodeChongYangMJ.prototype.EndRoom = function (tb, msg) {
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function(p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({userId: p.uid, lastGameTime: p.info.lastGameTime});
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if(!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('lastOffLineTime');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeChongYangMJ.prototype.calYingMo = function(tb, pl) {
    var tData = tb.tData;
    var cardssrc = pl.mjhand.slice();
    var hunNum = 0;
    for (var i = 0; i < cardssrc.length; i++) {
        if (cardssrc[i] == tData.hunCard) {
            hunNum++;
        }
    }
    var cards = pl.mjhand.slice(0, -1);
    var isYingMo = false;
    var Yingcards = cards;
    var YingtingSet = Object.keys(this.majiang.calTingSet(Yingcards));
    if(YingtingSet.length > 0 && YingtingSet.indexOf(String(tData.lastPutCard)) >= 0 && hunNum < 2){
        isYingMo = true;
    }else{
        isYingMo =false;
    }
    return isYingMo;
};

GameCodeChongYangMJ.prototype.huScore = function (tb, pl) {
    //算分
    var tData = tb.tData;
    tData.winner = tData.uids.indexOf(pl.uid);
    logger.debug("tData.winner:" + tData.winner);

    // var tingCards = Object.keys(this.majiang.calTingSet(pl.mjhand.slice(0, -1)));

    // 计算胡得分
    var score = 0;

    // 四癞
    var hunCount = this.majiang.getHunCount(pl.mjhand, tData);
    if (hunCount == 4) {
        score += 6;
        pl.mjdesc.push("四癞+6");
    }

    // 是否成牌型的胡
    var canHu = this.majiang.canHuByHunCard(pl, 0, tData);
    // 癞子牌统一替换为 200
    var mjHandCards = this.majiang.transformCards(pl.mjhand.slice(), tData.hunCard);
    // 胡牌牌型
    var qidui = this.majiang.is7Dui(mjHandCards); // 七对

    var qingyise = canHu && this.majiang.isSameColor(mjHandCards, pl.mjchi, pl.mjpeng, pl.mjgang0, pl.mjgang1); // 清一色
    var fengyise = canHu && this.majiang.isAllFeng(mjHandCards.concat(pl.mjchi).concat(pl.mjpeng).concat(pl.mjgang0).concat(pl.mjgang1)); // 风一色
    var jiangyise = canHu && this.majiang.isAllJiangColor(mjHandCards.concat(pl.mjchi).concat(pl.mjpeng).concat(pl.mjgang0).concat(pl.mjgang1)); // 将一色

    var isXiaoHu = false;

    if (qidui) {
        if (fengyise) {
            score += 48;
            pl.mjdesc.push("风七对+48");
        }
        else if (jiangyise) {
            score += 48;
            pl.mjdesc.push("将七对+48");
        }
        else if (qingyise) {
            score += 12;
            pl.mjdesc.push("清七对+12");
        }
        else {
            score += 6;
            pl.mjdesc.push("七对+6");
        }
    }
    else {
        if (fengyise) {
            score += 24;
            pl.mjdesc.push("风一色+24");
        }
        else if (jiangyise) {
            score += 24;
            pl.mjdesc.push("将一色+24");
        }
        else if (qingyise) {
            score += 6;
            pl.mjdesc.push("清一色+6");
        }
        else { // 小胡
            isXiaoHu = true;
        }
    }

    // 杠开
    if (this.majiang.huGangkai(tData, pl)) {
        if (isXiaoHu) {
            score += 6;
            pl.mjdesc.push("小胡杠开+6");
        }
        else {
            score *= 2;
            pl.mjdesc.push("大胡杠开*2");
        }
    }
    // 非杠开即为普通自摸
    else if (pl.winType == WinType.pickNormal) {
        if (isXiaoHu) {
            score += 2;
            pl.mjdesc.push("自摸+2");
        }
        else {
            pl.mjdesc.push("自摸");
        }
    }

    var yingHuFan = 1
    if (pl.isYingMo) {
        yingHuFan = 2;
        pl.mjdesc.push("硬胡*2");
    }
    
    // 飘分
    var plPiaoNum = pl.jiazhuNum;

    // 清一色包分
    var eatPlayer = null;
    if (qingyise && pl.eatCard > 2 && pl.eatInfo) {
        var eatId = tData.uids[pl.eatInfo.pos];
        eatPlayer = tb.getPlayer(eatId);
    }

    // 自摸
    if(pl.winType == WinType.pickNormal) {
        tb.AllPlayerRun(function (p) {
            if (p != pl) {
                var piao = p.jiazhuNum;
                var piaoScore = plPiaoNum + piao;
                if (eatPlayer) {
                    eatPlayer.mjdesc.push("包飘分-" + piaoScore);
                    pl.mjdesc.push("飘分+" + piaoScore);
                    var baseScore = score * yingHuFan + piaoScore;
                    eatPlayer.winone -= baseScore;
                    pl.winone += baseScore;
                }
                else {
                    p.mjdesc.push("飘分-" + piaoScore);
                    pl.mjdesc.push("飘分+" + piaoScore);
                    var baseScore = score * yingHuFan + piaoScore;
                    p.winone -= baseScore;
                    pl.winone += baseScore;
                }
            }
        });
    }
    // 点炮
    else {
        var dianPaoId = tData.uids[pl.dianPaoPlayer];
        var dianPaoPl = tb.getPlayer(dianPaoId);
        tb.AllPlayerRun(function (p) {
            if (p != pl) {
                var xiaohuScore = 0;
                if (isXiaoHu) {  
                    if (p == dianPaoPl) {
                        xiaohuScore = 2;
                        p.mjdesc.push("点炮-2");
                    }
                    else {
                        xiaohuScore = 1;
                        p.mjdesc.push("陪炮-1");
                    }
                }
                else {
                    p.mjdesc.push("点炮");
                }
                var piao = p.jiazhuNum;
                var piaoScore = plPiaoNum + piao;
                if (eatPlayer) {
                    eatPlayer.mjdesc.push("包飘分-" + piaoScore);
                    pl.mjdesc.push("飘分+" + piaoScore);
                    var baseScore = (score + xiaohuScore) * yingHuFan + piaoScore;
                    eatPlayer.winone -= baseScore;
                    pl.winone += baseScore;
                }
                else {
                    p.mjdesc.push("飘分-" + piaoScore);
                    pl.mjdesc.push("飘分+" + piaoScore);
                    var baseScore = (score + xiaohuScore) * yingHuFan + piaoScore;
                    p.winone -= baseScore;
                    pl.winone += baseScore;
                }
            }
        });
    }

    logger.debug("huScore=====结算结束=====");
};

// 过庄计算胡分大小
GameCodeChongYangMJ.prototype.huScoreNumber = function (tb, pl, cd) {
    //算分
    var tData = tb.tData;

    var mjhand = pl.mjhand.slice();
    logger.debug("===== mjhand ===== " , mjhand);
    logger.debug("======= cd ======= " , cd);
    if (cd) {
        mjhand.push(cd);
    }

    // 计算胡得分
    var score = 0;

    // 四癞
    var hunCount = this.majiang.getHunCount(mjhand, tData);
    if (hunCount == 4) {
        score += 6;
    }

    // 是否成牌型的胡
    var canHu = this.majiang.canHuByHunCard(pl, cd, tData);
    // 癞子牌统一替换为 200
    var mjHandCards = this.majiang.transformCards(mjhand.slice(), tData.hunCard);
    // 胡牌牌型
    var qidui = this.majiang.is7Dui(mjHandCards); // 七对

    var qingyise = canHu && this.majiang.isSameColor(mjHandCards, pl.mjchi, pl.mjpeng, pl.mjgang0, pl.mjgang1); // 清一色
    var fengyise = canHu && this.majiang.isAllFeng(mjHandCards.concat(pl.mjchi).concat(pl.mjpeng).concat(pl.mjgang0).concat(pl.mjgang1)); // 风一色
    var jiangyise = canHu && this.majiang.isAllJiangColor(mjHandCards.concat(pl.mjchi).concat(pl.mjpeng).concat(pl.mjgang0).concat(pl.mjgang1)); // 将一色

    var isXiaoHu = false;

    if (qidui) {
        if (fengyise) {
            score += 48;
        }
        else if (jiangyise) {
            score += 48;
        }
        else if (qingyise) {
            score += 12;
        }
        else {
            score += 6;
        }
    }
    else {
        if (fengyise) {
            score += 24;
        }
        else if (jiangyise) {
            score += 24;
        }
        else if (qingyise) {
            score += 6;
        }
        else { // 小胡
            score += 1;
        }
    }
    return score;
};

GameCodeChongYangMJ.prototype.isBtnReady = function(tb) {
    return false;
};

GameCodeChongYangMJ.prototype.EndGame = function (tb, pl, byEndRoom) {
    logger.debug("========GameCodeHZMJ.prototype.endGame========");

    var tData = tb.tData;
    var this_t = this;

    tb.AllPlayerRun(function (p) {
        p.mjState = TableState.roundFinish;
        p.mjdesc = p.mjdesc || [];
    });

    //胡牌分
    if (pl) {
        tb.AllPlayerRun(function (p) {
            if (p.winType > 0) {
                this_t.huScore(tb, p);
            }
        });
    }
    //流局
    else {
        tData.winner = -1;
    }

    if (byEndRoom) {
        tb.showDissmissDesc();
    }


    // 底分
    var difen = Number(tData.areaSelectMode["difen"] || 1);
    tb.AllPlayerRun(function (p) {
        p.winone = revise(p.winone * difen);
    });

    tData.tState = TableState.roundFinish;
    tb.AllPlayerRun(function(p) {
        if (!byEndRoom) {
            p.roomStatistics[tData.roundAll - tData.roundNum] = p.winone;
        }
    });

    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }

    tb.AllPlayerRun(function (p) {
        p.winall = revise(p.winall + p.winone);
    });
    tb.AllPlayerRun(function(p) {
        p.tableMsg.push(p.winone);
    });

    tb.perRoundEndDo();
    tData.roundNum--;
    var roundEnd = {
        players: tb.collectPlayer('mjhand', 'mjdesc', 'winone', 'winall', 'winType', 'baseWin', 'rate','dianpaoTotal','jiepaoTotal',
            'zimoTotal', 'minggangTotal', 'angangTotal', 'mjpeng', 'mjgang0', 'mjflower', 'info', 'lastOffLineTime','tableMsg'
        ),
        tData: tData,
        cards: tb.cards.slice(tData.cardNext),
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };
    
    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);//一局结束
    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb);//结束
    if (playInfo) roundEnd.playInfo = playInfo;

    logger.debug("EndGame============服务器告诉客户端一局完事了============");
    tb.NotifyAll("roundEnd", roundEnd);
};

GameCodeChongYangMJ.prototype.DestroyTable = function (tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
};


GameCodeChongYangMJ.prototype.mjJiazhu = function(pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (pl.mjState != TableState.waitJiazhu) return;

    pl.jiazhuNum = Number(msg.jiazhuNum);
    pl.mjState = TableState.isReady;
    tb.NotifyAll("MJJiazhu", {jiazhuNum : pl.jiazhuNum, uid : pl.uid });
    if (tb.AllPlayerCheck(function (p) {
        return p.mjState == TableState.isReady;
    })) {
        tb.runStartGame();
    }
};

GameCodeChongYangMJ.prototype.countZhuang = function (tData, tb) {
    logger.debug("===================GameCodeHZMJ.prototype.countZhuang==================");

    // 第一局
    if (tData.zhuang == -1) {
        // 随机坐庄
        tData.curPlayer = Math.floor(Math.random() * tData.maxPlayer) % tData.maxPlayer;
    }
    else if (tData.winner == -1) {
        if (tData.roundNum == tData.roundAll - 1) {
            // 第一局荒庄：第二局下家坐庄
            tData.curPlayer = (tData.zhuang + 1) % tData.maxPlayer;
        }
        else {
            // 第二局开始：荒庄则庄家连庄
            tData.curPlayer = tData.zhuang;
        }
    }
    else {
        // 胡牌的人坐庄
        tData.curPlayer = tData.winner;
    }
    tData.zhuang = tData.curPlayer;

    logger.debug("===================           countZhuang           ================== tData.zhuang = ", tData.zhuang);
};

module.exports = GameCodeChongYangMJ;