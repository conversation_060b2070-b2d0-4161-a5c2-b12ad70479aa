var config = {
	timeoutBreakOff: false,
	tableRound: 50,
	allHandsVisible: true,
	players: {
		Beta: "./PaodekuaiBeta.js",
	    Zero: "../PaodekuaiTYZero.js",
	    haha: "../PaodekuaiTYZero.js",
	    // Demo: "./PaodekuaiDemo.js",
	    // Trust: "./PaodekuaiTrust.js",
	    // Trust2: "./PaodekuaiTrust.js",
	},
}
config.testPlayers = Object.keys(config.players);	// 整体循环赛
config.testPlayers = ["Beta"];						// players[0] 固定为某个AI
module.exports = config;