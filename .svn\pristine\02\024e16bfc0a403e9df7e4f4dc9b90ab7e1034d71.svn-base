{"_args": [["extend@3.0.0", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "extend@3.0.0", "_id": "extend@3.0.0", "_inBundle": false, "_integrity": "sha1-WkdDU7nzNT3dgXbf03uRyDpG8dQ=", "_location": "/extend", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "extend@3.0.0", "name": "extend", "escapedName": "extend", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/", "/jpush-sdk/request"], "_resolved": "https://registry.npmjs.org/extend/-/extend-3.0.0.tgz", "_spec": "3.0.0", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "dependencies": {}, "description": "Port of jQuery.extend for node.js and the browser", "devDependencies": {"covert": "^1.1.0", "eslint": "^0.24.0", "jscs": "^1.13.1", "tape": "^4.0.0"}, "homepage": "https://github.com/justmoon/node-extend#readme", "keywords": ["extend", "clone", "merge"], "license": "MIT", "main": "index", "name": "extend", "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "scripts": {"coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "eslint": "eslint *.js */*.js", "jscs": "jscs *.js */*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node test/index.js && npm run coverage-quiet"}, "version": "3.0.0"}