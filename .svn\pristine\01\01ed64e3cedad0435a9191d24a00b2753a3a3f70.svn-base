var fs = require('fs');
//var md5 = require('md5');
var CryptoJS = require("crypto-js");
var path = require('path');
var util = require('util');
var moment = require('moment');
var crypto = require('crypto');
var request = require('request');
var xlsx = require('node-xlsx');
var express = require('express');
var excelExport = require('excel-export');
var key = CryptoJS.enc.Utf8.parse("abc321fed456hs87");
var iv = CryptoJS.enc.Utf8.parse("ijkl9012ponm6543");
var isContains = function(arr, str) {
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == str) {
            return true;
        }
    }
    return false;
};

module.exports = {
    dispatch: function(pkroomList, pkroomCount) {
        return pkroomList[parseInt(Math.random() * pkroomCount)].id;
    },

    inherits: util.inherits,

    moment: function(args) {
        if (args) {
            return moment(args);
        }

        return moment();
    },

    md5: function(msg) {
        return CryptoJS.MD5.md5(msg);
        //return md5(msg);
    },

    dateFormat: function(date, format) {
        if (!date) date = new Date();
        if (!format) format = 'yyyy-MM-dd hh:mm:ss';
        var millisecond = date.getMilliseconds();
        if (millisecond < 10) {
            millisecond = '00' + millisecond;
        }
        else if (millisecond < 100) {
            millisecond = '0' + millisecond;
        }
        var o = {
            'M+': date.getMonth() + 1,
            'd+': date.getDate(),
            'h+': date.getHours(),
            'm+': date.getMinutes(),
            's+': date.getSeconds(),
            'q+': Math.floor((date.getMonth() + 3) / 3),
            'S+': millisecond
        };
        if (/(y+)/.test(format)) {
            format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp('(' + k + ')').test(format)) {
                format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
            }
        }
        return format;
    },

    secondFormat: function(s) {
        s = parseInt(s);
        var second = parseInt(s % 60);
        var minute = parseInt(s / 60);
        if (second < 10) {
            second = '0' + second;
        }
        if (minute < 10) {
            minute = '0' + minute;
        }
        return minute + ':' + second;
    },

    isArray: function(obj) {
        return Array.isArray(obj);
    },

    isBoolean: function(obj) {
        return typeof obj === 'boolean';
    },

    isNull: function(obj) {
        return obj === null;
    },

    isNullOrUndefined: function(obj) {
        return obj === null || obj === undefined;
    },

    isNumber: function(obj) {
        return typeof obj === 'number';
    },

    isString: function(obj) {
        return typeof obj === 'string';
    },

    isSymbol: function(obj) {
        return typeof obj === 'symbol';
    },

    isUndefined: function(obj) {
        return obj === void 0;
    },

    isPrimitive: function(obj) {
        return obj === null || typeof obj === 'boolean' || typeof obj === 'number' || typeof obj === 'string' || typeof obj === 'symbol' || typeof obj === 'undefined';
    },

    isInteger: function(obj) {
        return typeof obj === 'number';
    },

    isObject: function(obj) {
        return typeof obj === 'object';
    },

    isTimer: function(obj) {
        if (!obj) {
            return false;
        }
        return typeof obj === "object" && ( '_idleStart', '_idleNext', '_idlePrev' in obj);
    },

    isFunction: function(obj) {
        return typeof obj === "function";
    },

    isNumOrStrNum: function(obj) {
        return obj == parseInt(obj);
    },

    isValidEmail: function(obj) {
        return /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(obj)
    },

    isValidUrl: function(obj) {
        return /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?/.test(obj);
    },

    isValidMobileNum: function(obj) {
        return /^1[0-9]{10}$/.test(obj);
    },

    isValidIdentityNum: function(obj) {
        return /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/.test(obj);
    },

    isValidBankNum: function(obj) {
        return /\d{15}|\d{19}/.test(obj);
    },

    isEmptyObject: function(obj) {
        if (typeof obj != 'object') {
            return true;
        }
        for (var key in obj) {
            return false
        }
        return true;
    },

    isStrNotEmpty: function(obj) {
        return typeof obj != 'undefined' && obj != undefined && obj != null && obj != 'NaN' && obj != NaN && obj.toString().replace(/^\s+|\s+$/g, '') != '';
    },

    isParamsNotEmpty: function(array) {
        for (var i in array) {
            if (!this.isStrNotEmpty(array[i])) {
                return false;
            }
        }
        return true;
    },

    isContains: isContains,

    stringFormat: function() {
        var args = [];
        var str = arguments[0];
        for (var i = 1; i < arguments.length; i++) {
            args.push(arguments[i]);
        }
        return str.replace(/\{(\d+)\}/g, function(s, i) {
            return args[i];
        });
    },

    stringLengthForMysql: function(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            var c = str.charCodeAt(i);
            if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                len++;
            }
            else {
                len += 3;
            }
        }
        return len;
    },

    getStrLength: function(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            var c = str.charCodeAt(i);
            if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                len++;
            }
            else {
                len += 2;
            }
        }
        return len;
    },

    getClientIp: function(req) {
        var ipAddress;
        var clientIp = req.headers['x-client-ip'];
        var forwardedForAlt = req.headers['x-forwarded-for'];
        var realIp = req.headers['x-real-ip'];
        var clusterClientIp = req.headers['x-cluster-client-ip'];
        var forwardedAlt = req.headers['x-forwarded'];
        var forwardedFor = req.headers['forwarded-for'];
        var forwarded = req.headers['forwarded'];
        if (clientIp) {
            ipAddress = clientIp;
        }
        else if (forwardedForAlt) {
            ipAddress = forwardedForAlt.split(',')[0];
        }
        else if (realIp) {
            ipAddress = realIp;
        }
        else if (clusterClientIp) {
            ipAddress = clusterClientIp;
        }
        else if (forwardedAlt) {
            ipAddress = forwardedAlt;
        }
        else if (forwardedFor) {
            ipAddress = forwardedFor;
        }
        else if (forwarded) {
            ipAddress = forwarded;
        }
        if (!ipAddress) {
            try {
                ipAddress = req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress || null;
            }
            catch (e) {
                ipAddress = null;
            }
        }
        return ipAddress;
    },

    getClientOs: function(req) {
        var ua = req.headers['user-agent'];
        var iPad = ua.match(/(iPad).*OS\s([\d_]+)/);
        var iPhone = !iPad && ua.match(/(iPhone\sOS)\s([\d_]+)/);
        var android = ua.match(/(Android)\s+([\d.]+)/);
        var os = 'android';
        if (iPad) os = 'ios';
        if (iPhone) os = 'ios';
        return os;
    },

    // 去重
    arrayDistinct: function(srcArray) {
        var dstArray = [], isRepeated;
        for (var i = 0; i < srcArray.length; i++) {
            isRepeated = false;
            for (var j = 0; j < dstArray.length; j++) {
                if (srcArray[i] == dstArray[j]) {
                    isRepeated = true;
                    break;
                }
            }
            if (!isRepeated) {
                dstArray.push(srcArray[i]);
            }
        }
        return dstArray;
    },

    // 找出重复
    arrayDuplicate: function(srcArray) {
        var dstArray = [];
        srcArray = srcArray.sort();
        for (var i = 1; i < srcArray.length; i++) {
            if (srcArray[i] == srcArray[i - 1] && dstArray.indexOf(srcArray[i]) == -1) {
                dstArray.push(srcArray[i]);
            }
        }
        return dstArray;
    },

    arrayGetItem: function(array, key, value) {
        if (typeof array != "object") {
            return null;
        }
        if (!this.isStrNotEmpty(value)) {
            return null;
        }
        var ret = null;
        for (var i = 0; i < array.length; i++) {
            if (array[i][key] == value) {
                ret = array[i];
                break;
            }
        }
        return ret;
    },

    //1. 去除空字符、sign
    filter: function(obj) {
        var newobj = {};
        for (var key in obj) {
            if (key === 'sign' || obj[key] === '')
                continue;
            newobj[key] = obj[key];
        }
        return newobj;
    },

    //2. 升序排序
    sort: function(obj) {
        var newobj = {};
        var keys = [];
        var i, j, key;
        for (key in obj) {
            keys.push(key);
        }
        for (i = 0; i < keys.length; i++) {
            for (j = i + 1; j < keys.length; j++) {
                if (keys[i] > keys[j]) {
                    key = keys[i];
                    keys[i] = keys[j];
                    keys[j] = key;
                }
            }
        }
        for (i = 0; i < keys.length; i++) {
            key = keys[i];
            newobj[key] = obj[key];
        }
        return newobj;
    },

    //3.拼接字符串
    link: function(obj) {
        var str = '';
        for (var key in obj) {
            str += key + '=' + obj[key] + '&';
        }
        return str.substring(0, str.length - 1);
    },

    formatPubKey: function(publicKey) {
        var fKey = '-----BEGIN PUBLIC KEY-----\n';
        var len = publicKey.length;
        for (var i = 0; i < len;) {
            fKey = fKey + publicKey.substr(i, 64) + '\n';
            i += 64;
        }
        fKey += '-----END PUBLIC KEY-----';
        return fKey;
    },

    formatPriKey: function(privateKey) {
        var fKey = '-----BEGIN RSA PRIVATE KEY-----\n';
        var len = privateKey.length;
        for (var i = 0; i < len;) {
            fKey = fKey + privateKey.substr(i, 64) + '\n';
            i += 64;
        }
        fKey += '-----END RSA PRIVATE KEY-----';
        return fKey;
    },

    sign: function(privateKey, data, algorithm) {
        if (!algorithm) {
            algorithm = 'RSA-SHA256';
        }
        var signer = crypto.createSign(algorithm);
        signer.update(data, 'utf8');
        return signer.sign(this.formatPriKey(privateKey), 'base64');
    },

    verify: function decipher(publicKey, data, sign, algorithm) {
        if (!algorithm) {
            algorithm = 'RSA-SHA256';
        }
        var verify = crypto.createVerify(algorithm);
        verify.update(data, 'utf8');
        return verify.verify(this.formatPubKey(publicKey), sign, 'base64');
    },

    hmacSign: function(data, secretKey) {
        var hmac = crypto.createHmac('sha256', secretKey);
        hmac.update(data);
        return hmac.digest('hex');
    },

    base64: function(str) {
        return new Buffer(str).toString('base64');
    },

    randomRange: function(begin, end) {
        if (typeof begin != "number" || typeof end != "number") {
            return null;
        }
        if (!begin) begin = 0;
        if (!end) end = 0;
        return Number(begin + Math.random() * (end - begin)).toFixed(2);
    },

    randomNum: function(length) {
        if (!length) {
            length = 6;
        }
        var numStr = '';
        var selectChar = '0123456789';
        for (var i = 0; i < length; i++) {
            var charIndex = Math.floor(Math.random() * selectChar.length);
            numStr += selectChar[charIndex];
        }
        return numStr;
    },

    randomStr: function(length) {
        if (!length) {
            length = 8;
        }
        var randomStr = '';
        var selectChar = 'ABCDEFGHIJKLMNOPQRSTUVWXTZ0123456789abcdefghijklmnopqrstuvwxyz';
        for (var i = 0; i < length; i++) {
            var index = Math.floor(Math.random() * selectChar.length);
            randomStr += selectChar[index];
        }
        return randomStr;
    },

    randomIp: function() {
        return Math.floor(1 + Math.random() * 254) + '.' + Math.floor(1 + Math.random() * 254) + '.' + Math.floor(1 + Math.random() * 254) + '.' + Math.floor(1 + Math.random() * 254);
    },

    shuffle: function(arr) {
        var len = arr.length;
        var idx = len;
        var tmp;
        var rdm;

        while (idx--) {
            rdm = Math.floor(Math.random() * len)
            if (idx !== rdm) {
                tmp = arr[idx];
                arr[idx] = arr[rdm];
                arr[rdm] = tmp;
            }
        }

        return arr;
    },

    createDirectory: function(fullPath) {
        if (fs.existsSync(fullPath)) {
            return true;
        }
        else {
            if (this.createDirectory(path.dirname(fullPath))) {
                fs.mkdirSync(fullPath);
                return true;
            }
        }
    },

    downloadUrlToDir: function(urlList, dirname) {
        var self = this;
        for (var i = 0; i < urlList.length; i++) {
            var url = urlList[i];
            var first = url.lastIndexOf('/') + 1;
            if (first > 0) {
                var name = url.substring(first);
                var dstpath = dirname + '/' + name;
                if (self.createDirectory(dirname)) {
                    request(url).pipe(fs.createWriteStream(dstpath));
                }
            }
        }
    },

    copyPtys: function(from, to, ptys) {
        if (!to) {
            to = {};
        }
        if (!ptys) {
            ptys = Object.keys(from);
        }
        else if (!Array.isArray(ptys)) {
            ptys = ptys.split(',');
        }
        for (var i = 0; i < ptys.length; i++) {
            to[ptys[i]] = from[ptys[i]];
        }
        return to;
    },

    loadModules: function(path) {
        var modules = {};
        (function work(path) {
            var files = fs.readdirSync(path);
            files.forEach(function(item) {
                var tmpPath = path + '/' + item;
                var stats = fs.statSync(tmpPath);
                if (item[0] == '.') {
                    return;
                }
                if (stats.isDirectory()) {
                    work(tmpPath, item);
                }
                else {
                    item = item.split('.')[0];
                    delete require.cache[require.resolve(tmpPath)];
                    modules[item] = require(tmpPath);
                }
            });
        })(path);
        return modules;
    },
    loadRoutes: function(path) {
        var routers = {};
        (function work(path, parent) {
            var files = fs.readdirSync(path);
            files.forEach(function(item) {
                var tmpPath = path + '/' + item;
                var stats = fs.statSync(tmpPath);
                if (item[0] == '.') {
                    return;
                }
                if (stats.isDirectory()) {
                    routers[item] = express();
                    try {
                        routers[item] = require(tmpPath + '/index');
                    }
                    catch (err) {
                    }
                    work(tmpPath, item);
                }
                else if (parent != '') {
                    item = item.split('.')[0];
                    routers[parent].use('/' + item, require(tmpPath));
                }
                else {
                    item = item.split('.')[0];
                    if (item != 'index') {
                        routers[item] = require(tmpPath);
                    }
                }
            });
        })(path, '');
        return routers;
    },

    pipe: function(stream, fn) {
        var buffers = [];
        stream.on('data', function(trunk) {
            buffers.push(trunk);
        });
        stream.on('end', function() {
            fn(null, Buffer.concat(buffers));
        });
        stream.once('error', fn);
    },

    clone: function(obj) {
        var o;
        if (typeof obj == 'object') {
            if (obj === null) {
                o = null;
            }
            else {
                if (obj instanceof Array) {
                    o = [];
                    for (var i = 0, len = obj.length; i < len; i++) {
                        o.push(this.clone(obj[i]));
                    }
                }
                else {
                    o = {};
                    for (var j in obj) {
                        o[j] = this.clone(obj[j]);
                    }
                }
            }
        }
        else {
            o = obj;
        }
        return o;
    },

    sleep: function(sleepTime) {
        for (var start = Date.now(); Date.now() - start <= sleepTime;) {
        }
    },

    /**
     * 获取两个日期间所有的日期
     * @param begin
     * @param end
     * @returns {Array}
     */
    getAllDate: function(begin, end) {
        var ab = begin.split('-');
        var ae = end.split('-');
        var db = new Date();
        var de = new Date();
        var ret = [];
        db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
        de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
        var unixDb = db.getTime();
        var unixDe = de.getTime();
        for (var k = unixDb; k <= unixDe;) {
            ret.push(parseInt(k));
            k = k + 24 * 60 * 60 * 1000;
        }
        return ret;
    },

    /**
     * 获取两个日期间所有的日期
     * @param begin
     * @param end
     * @param fmt
     * @returns {Array}
     */
    getAllDateStr: function(begin, end, fmt) {
        if (!fmt) fmt = 'yyyy-MM-dd';
        var ab = begin.split('-');
        var ae = end.split('-');
        var db = new Date();
        var de = new Date();
        var ret = [];
        db.setFullYear(ab[0], ab[1] - 1, ab[2]);
        de.setFullYear(ae[0], ae[1] - 1, ae[2]);
        var unixDb = db.getTime();
        var unixDe = de.getTime();
        for (var k = unixDb; k <= unixDe;) {
            ret.push(this.dateFormat(new Date(parseInt(k)), fmt));
            k = k + 24 * 60 * 60 * 1000;
        }
        return ret;
    },

    specialUrlEncode: function(value) {
        return encodeURIComponent(value).replace('+', '%20').replace('*', '%2A').replace('%7E', '~');
    },

    /**
     * 获取两个日期间所有的周
     * @param begin
     * @param end
     * @param fmt
     * @example getWeekAll('2018-04-10', '2018-04-24', 'yyyyMMdd')
     * @returns {Array} [ '20180410', '20180417', '20180424' ]
     */
    getWeekAll: function(begin, end, fmt) {
        var dateAllArr = new Array();
        if (!fmt) fmt = 'yyyy-MM-dd';
        var ab = begin.split("-");
        var ae = end.split("-");
        var db = new Date();
        db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
        var de = new Date();
        de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
        var unixDb = db.getTime();
        var unixDe = de.getTime();
        for (var k = unixDb; k <= unixDe;) {
            dateAllArr.push(this.dateFormat(new Date(parseInt(k)), fmt));
            k = k + 7 * 24 * 60 * 60 * 1000;
        }
        return dateAllArr;
    },

    /**
     * 获取两个日期间所有的月
     * @param begin
     * @param end
     * @example getMonthAll('2018-03-10', '2018-04-19')
     * @returns {Array} [ '20180301', '20180401' ]
     */
    getMonthAll: function(begin, end) {
        var d1 = begin;
        var d2 = end;
        var dateArry = new Array();
        var s1 = d1.split("-");
        var s2 = d2.split("-");
        var mCount = 0;
        if (parseInt(s1[0]) < parseInt(s2[0])) {
            mCount = (parseInt(s2[0]) - parseInt(s1[0])) * 12 + parseInt(s2[1]) - parseInt(s1[1]) + 1;
        }
        else {
            mCount = parseInt(s2[1]) - parseInt(s1[1]) + 1;
        }
        if (mCount > 0) {
            var startM = parseInt(s1[1]);
            var startY = parseInt(s1[0]);
            for (var i = 0; i < mCount; i++) {
                if (startM < 12) {
                    dateArry[i] = startY + (startM > 9 ? startM + '' : "0" + startM) + '01';
                    startM += 1;
                }
                else {
                    dateArry[i] = startY + (startM > 9 ? startM + '' : "0" + startM) + '01';
                    startM = 1;
                    startY += 1;
                }
            }
        }
        return dateArry;
    },

    /**
     * 获取两个日期间所有的年
     * @param begin
     * @param end
     * @example getYearAll('2017-03-10', '2018-04-19')
     * @returns {Array} [ 2017, 2018 ]
     */
    getYearAll: function(begin, end) {
        var d1 = begin;
        var d2 = end;
        var dateArry = new Array();
        var s1 = d1.split("-");
        var s2 = d2.split("-");
        var mYearCount = parseInt(s2[0]) - parseInt(s1[0]) + 1;
        var startY = parseInt(s1[0]);
        for (var i = 0; i < mYearCount; i++) {
            dateArry[i] = startY;
            startY += 1;
        }
        return dateArry;
    },

    /**
     * 把秒转换成日、时、分、秒
     * @param value 单位秒
     */
    formatSeconds: function(value) {
        var secondTime = parseInt(value);// 秒
        var minuteTime = 0;// 分
        var hourTime = 0;// 小时
        var day = 0;// 天
        if (secondTime > 60) {//如果秒数大于60，将秒数转换成整数
            //获取分钟，除以60取整数，得到整数分钟
            minuteTime = parseInt(secondTime / 60);
            //获取秒数，秒数取佘，得到整数秒数
            secondTime = parseInt(secondTime % 60);
            //如果分钟大于60，将分钟转换成小时
            if (minuteTime > 60) {
                //获取小时，获取分钟除以60，得到整数小时
                hourTime = parseInt(minuteTime / 60);
                //获取小时后取佘的分，获取分钟除以60取佘的分
                minuteTime = parseInt(minuteTime % 60);
                //获取天数
                if (hourTime > 24) {
                    day = parseInt(hourTime / 24);
                    hourTime = parseInt(hourTime % 24);
                }
            }
        }
        var result = {day: day, hour: 0, minute: 0, second: secondTime};

        if (minuteTime > 0) {
            result.minute = parseInt(minuteTime);
        }
        if (hourTime > 0) {
            result.hour = parseInt(hourTime);
        }
        if (!day) {
            delete result.day;
        }
        return result;
    },

    readExcel: function(fullName) {
        var data;
        try {
            data = xlsx.parse(fullName);
        }
        catch (err) {
            data = null;
        }
        return data;
    },

    /**
     * 单页excel
     * @param filename 文件名
     * @param data 数据
     * @param callback
     */
    createExcel: function(filename, data, callback) {
        var filePath = rootdir + '/document/' + filename;
        var sheet = {
            cols: [{caption: '编号', type: 'number', width: 10}],
            rows: []
        };
        for (var key in data[0]) {
            sheet.cols.push({
                caption: key,
                type: typeof(data[0][key]),
                width: this.getStrLength(key) + 4 > 12 ? this.getStrLength(key) + 4 : 12
            });
        }
        for (var i in data) {
            sheet.rows[i] = [];
            sheet.rows[i][0] = parseInt(i) + 1;
            for (var j = 1; j < sheet.cols.length; j++) {
                sheet.rows[i][j] = data[i][sheet.cols[j].caption];
            }
        }
        var file = excelExport.execute(sheet);
        fs.writeFile(filePath, file, 'binary', (err) => { callback(err, filePath) });
    },

    _createExcel: function(filename, data, callback) {
        var filePath = rootdir + '/web/' + filename;
        var sheet = {
            cols: [],
            rows: []
        };
        for (var key in data[0]) {
            sheet.cols.push({
                caption: key,
                type: typeof(data[0][key]),
                width: this.getStrLength(key) + 4 > 12 ? this.getStrLength(key) + 4 : 12
            });
        }
        for (var i in data) {
            sheet.rows[i] = [];
            for (var j = 0; j < sheet.cols.length; j++) {
                sheet.rows[i][j] = data[i][sheet.cols[j].caption];
            }
        }
        var file = excelExport.execute(sheet);
        fs.writeFile(filePath, file, 'binary', (err) => { callback(err, filePath) });
    },

    /**
     * 多页excel
     * @param filename 文件名
     * @param list 数据组
     * @param callback
     */
    createExcelMulti: function(filename, list, callback) {
        var filePath = rootdir + '/document/' + filename;
        var sheets = [];
        for (var i = 0; i < list.length; i++) {
            var sheet = {
                name: list[i].name,
                data: []
            };
            var head = ['编号'];
            for (var key in list[i].data[0]) {
                head.push(key);
            }
            sheet.data.push(head);
            for (var j = 0; j < list[i].data.length; j++) {
                var row = [j + 1];
                for (var value in list[i].data[j]) {
                    row.push(list[i].data[j][value]);
                }
                sheet.data.push(row);
            }
            sheets.push(sheet);
        }
        var file = xlsx.build(sheets);
        fs.writeFile(filePath, file, 'binary', (err) => { callback(err, filePath) });
    },

    //获取2个gps坐标距离
    distance: function(lng1, lat1, lng2, lat2) {
        var radLat1 = lat1 * Math.PI / 180;
        var radLat2 = lat2 * Math.PI / 180;
        var deltaLat = radLat1 - radLat2;
        var deltaLng = (lng1 - lng2) * Math.PI / 180;
        var dis = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(deltaLat / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(deltaLng / 2), 2)));
        return Math.round(dis * 6378137);
    },

    response: function(res, data) {
        res.send({code: data.code, message: data.message, env: env, data: data.data});
        logger.info('[响应][' + this.getClientIp(res.req) + ']', JSON.stringify({code: data.code, message: data.message, env: env, data: data.data}));
    },

    daiLiMoShi: function() {
        if (isContains(['nantong'], env.replace('-test', ''))) {
            return 'agent';
        }
        else {
            return 'member';
        }
    },

    qrImage: function(url, size, callback) {
        if (!size) size = 2;
        var image = qr.image(url, {type: 'png', size: size});
        var filename = rootdir + '/document/' + Date.now() + '.jpg';
        var pipe = image.pipe(fs.createWriteStream(filename));
        pipe.on('error', function(err) {
            callback(err, null);
        });
        pipe.on('finish', function() {
            callback(null, filename);
        });
    },

    qrReader: function(url, callback) {
        request({url: 'http://kuai.xiaohuaerai.com/kuai', method: 'POST', headers: {'Authorization': 'APPCODE 9ed1d15b65f0440ba25cd128798ad3f8'}, json: true, form: {speed: 'fast', src: encodeURI(url)}}, function(err, rsp, body) {
            if (body && body.status == 200) {
                callback(body.msg);
            }
            else {
                callback(null);
            }
        });
    },

    txtFilter: function(str) {
        if (!str) {
            str = '';
        }
        return str.toString()
        .replace(/快/g, '*')
        .replace(/筷/g, '*')
        .replace(/块/g, '*')
        .replace(/塊/g, '*')
        .replace(/員/g, '*')
        .replace(/缘/g, '*')
        .replace(/圆/g, '*')
        .replace(/园/g, '*')
        .replace(/園/g, '*')
        .replace(/圓/g, '*')
        .replace(/元/g, '*')
        .replace(/角/g, '*')
        .replace(/脚/g, '*')
        .replace(/毛/g, '*')
        .replace(/茅/g, '*')
        .replace(/赌/g, '*')
        .replace(/红包/g, '**')
        .replace(/钱/g, '*')
        .replace(/福利/g, '**')
        .replace(/分/g, '*')
        .replace(/芬/g, '*')
        .replace(/跑包/g, '**')
        .replace(/押金/g, '**')
        .replace(/房费/g, '**')
        .replace(/抽水/g, '**')
        .replace(/反水/g, '**')
        .replace(/结账/g, '**')
        .replace(/收款/g, '**')
        .replace(/收钱/g, '**')
        .replace(/上/g, '**')
        .replace(/下/g, '**')
        .replace(/一千/g, '**')
        .replace(/1000/g, '**')
        .replace(/微信/g, '**')
        .replace(/¥/g, '*')
        .replace(/\$/g, '*');
    },

    matchTxtFilter: function(str) {
        if (!str) {
            return '';
        }
        str = this.txtFilter(str);
        return str.toString()
        .replace(/1000/g, '**')
        .replace(/一千/g, '**')
        .replace(/上/g, '*')
        .replace(/下/g, '*')
        .replace(/分/g, '*')
        .replace(/芬/g, '*');
    },

    // 局数转换场次，1局的场算 0.2
    roundNumConvertGameCnt: function(roundNum) {
        if (this.isStrNotEmpty(roundNum) && roundNum == 1) {
            return 0.2;
        }
        else {
            return 1;
        }
    },

    // num 要替换的字，start 前面保留几位 end 后面保留几位
    replaceNumWithStars: function(num, start, end) {
        if (num != parseInt(num)) {
            return num;
        }
        var arr = (num + '').split('');
        start = start || 0;
        end = end || 0;
        var len = arr.length;
        if (start + end < len) {
            for (var i = 0; i < len; i++) {
                if (i >= start && len - i > end) {
                    arr[i] = '*';
                }
            }
        }
        return arr.join('');
    },

    ruleDesc: require('./lib/desc-js3mj'),

    number_check1: function(val) {
        // 查询是否是数字
        return /^[0-9]{1,}$/.test(val);
    },

    number_check2: function(val) {
        // 查询是否是数字,包括负数
        return /^[\-]{0,1}[0-9]{1,}$/.test(val);
    },

    str_noempty_check: function(val) {
        // 查询是否有空格 ，有空格返回假
        if (!val) {
            return true;
        }
        if (typeof (val) != "string") {
            return false;
        }
        var ret = val.indexOf(" ");
        if (ret == -1) {
            return true;
        }
        return false;
    },

    time_fmt_check1: function(val) {
        // 时间格式 yyyy-mm-dd hh:mi:ss yyyy/mm/dd hh:mi:ss
        return /^([0-9]{4}[-/][0-9]{1,2}[-/][0-9]{2} [0-9]{2}:[0-9]{2}[:]{0,1}[0-9]{0,2})$/.test(val);
    },

    time_fmt_check2: function(val) {
        // 时间格式 yyymmdd hh:mi:ss
        return /^([0-9]{8} [0-9]{2}:[0-9]{2}[:]{0,1}[0-9]{0,2})$/.test(val);
    },

    time_fmt_check3: function(val) {
        // 时间格式 yyyy-mm-dd
        return /^([0-9]{4}-[0-9]{2}-[0-9]{2})$/.test(val);
    },

    paramsCheck: function(_param) {
        if (!_param) {
            return false;
        }
        for (var i = 0; i < _param.length; i++) {
            var row = _param[i];
            switch (row.type) {
                case 'string'://字符串
                    if (row.not_null) {
                        if (!row.value) {
                            return false;
                        }
                        if (row.value.length == 0) {
                            return false;
                        }
                    }
                    else {
                        if (!row.value) {
                            continue;
                        }
                    }
                    if (typeof (row.value) != "string") {
                        return false;
                    }
                    if (row.no_blank) {
                        if (!this.str_noempty_check(row.value)) {
                            return false;
                        }
                    }
                    if (row.in) {
                        if (row.in instanceof Array) {
                            var valArr = row.value.toString().split(',');
                            for (var m = 0; m < valArr.length; m++) {
                                var find = false;
                                for (var k = 0; k < row.in.length; k++) {
                                    if (row.in[k] == valArr[m]) {
                                        find = true;
                                        break;
                                    }
                                }
                                if (!find) {
                                    return false;
                                }
                            }
                        }
                    }
                    break;
                case 'number'://正整数和0
                    if (row.not_null) {
                        if (!this.number_check1(row.value)) {
                            return false;
                        }
                    }
                    if (!row.value) {
                        continue;
                    }
                    if (!this.number_check1(row.value)) {
                        return false;
                    }
                    if (row.in) {
                        if (row.in instanceof Array) {
                            var val = parseInt(row.value);
                            var find = false;
                            for (var k = 0; k < row.in.length; k++) {
                                if (row.in[k] == val) {
                                    find = true;
                                    break;
                                }
                            }
                            if (!find) {
                                return false;
                            }
                        }
                    }
                    break;
                case 'number2'://整数，包括负数
                    if (row.not_null) {
                        if (!this.number_check2(row.value)) {
                            return false;
                        }
                    }
                    if (!row.value) {
                        continue;
                    }
                    if (!this.number_check2(row.value)) {
                        return false;
                    }
                    if (row.in) {
                        if (row.in instanceof Array) {
                            var val = parseInt(row.value);
                            var find = false;
                            for (var k = 0; k < row.in.length; k++) {
                                if (row.in[k] == val) {
                                    find = true;
                                    break;
                                }
                            }
                            if (!find) {
                                return false;
                            }
                        }
                    }
                    break;
                case "float":
                    if (row.not_null) {
                        if (isNaN(parseFloat(row.value))) {
                            return false;
                        }
                        continue;
                    }
                    if (!row.value) {
                        continue;
                    }
                    if (isNaN(parseFloat(row.value))) {
                        return false;
                    }
                    break;
                case 'date':
                    if (row.not_null) {
                        if (!this.time_fmt_check2(row.value)) {
                            return false;
                        }
                        continue;
                    }
                    if (!row.value) {
                        continue;
                    }
                    if (!this.time_fmt_check2(row.value)) {
                        return false;
                    }
                    break;
            }
        }

        return true;
    },
    // 用户商场的加密签名
    mallSign: function(content) {
        //这两个hex是跟mall项目的一致的，请不要随意更改
        var hex1 = '6D2a';
        var hex2 = '1F56d96D20799e7a';

        function md5(content) {
            return crypto.createHash('md5').update(content).digest("hex")
        }

        return md5(hex1 + md5(content) + hex2);
    },
    str2binl : function (str){
        var bin = Array();
        var mask = (1 << 8) - 1;
        for(var i = 0; i < str.length * 8; i += 8)
            bin[i>>5] |= (str.charCodeAt(i / 8) & mask) << (i%32);
        return bin;
    },
    bit_rol : function (num, cnt) {
        return (num << cnt) | (num >>> (32 - cnt));
    },
    safe_add : function (x, y) {
        var lsw = (x & 0xFFFF) + (y & 0xFFFF);
        var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
        return (msw << 16) | (lsw & 0xFFFF);
    },
    md5_cmn : function (q, a, b, x, s, t) {
        return this.safe_add(this.bit_rol(this.safe_add(this.safe_add(a, q), this.safe_add(x, t)), s),b);
    },
    md5_ff : function (a, b, c, d, x, s, t) {
        return this.md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);
    },    
    md5_gg : function (a, b, c, d, x, s, t) {
        return this.md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);
    },    
    md5_hh : function (a, b, c, d, x, s, t) {
        return this.md5_cmn(b ^ c ^ d, a, b, x, s, t);
    },    
    md5_ii : function (a, b, c, d, x, s, t) {
        return this.md5_cmn(c ^ (b | (~d)), a, b, x, s, t);
    },
    core_md5 : function (x, len) {
        /* append padding */
        x[len >> 5] |= 0x80 << ((len) % 32);
        x[(((len + 64) >>> 9) << 4) + 14] = len;

        var a =  1732584193;
        var b = -271733879;
        var c = -1732584194;
        var d =  271733878;

        for(var i = 0; i < x.length; i += 16) {
            var olda = a;
            var oldb = b;
            var oldc = c;
            var oldd = d;

            a = this.md5_ff(a, b, c, d, x[i+ 0], 7 , -680876936);
            d = this.md5_ff(d, a, b, c, x[i+ 1], 12, -389564586);
            c = this.md5_ff(c, d, a, b, x[i+ 2], 17,  606105819);
            b = this.md5_ff(b, c, d, a, x[i+ 3], 22, -1044525330);
            a = this.md5_ff(a, b, c, d, x[i+ 4], 7 , -176418897);
            d = this.md5_ff(d, a, b, c, x[i+ 5], 12,  1200080426);
            c = this.md5_ff(c, d, a, b, x[i+ 6], 17, -1473231341);
            b = this.md5_ff(b, c, d, a, x[i+ 7], 22, -45705983);
            a = this.md5_ff(a, b, c, d, x[i+ 8], 7 ,  1770035416);
            d = this.md5_ff(d, a, b, c, x[i+ 9], 12, -1958414417);
            c = this.md5_ff(c, d, a, b, x[i+10], 17, -42063);
            b = this.md5_ff(b, c, d, a, x[i+11], 22, -1990404162);
            a = this.md5_ff(a, b, c, d, x[i+12], 7 ,  1804603682);
            d = this.md5_ff(d, a, b, c, x[i+13], 12, -40341101);
            c = this.md5_ff(c, d, a, b, x[i+14], 17, -1502002290);
            b = this.md5_ff(b, c, d, a, x[i+15], 22,  1236535329);

            a = this.md5_gg(a, b, c, d, x[i+ 1], 5 , -165796510);
            d = this.md5_gg(d, a, b, c, x[i+ 6], 9 , -1069501632);
            c = this.md5_gg(c, d, a, b, x[i+11], 14,  643717713);
            b = this.md5_gg(b, c, d, a, x[i+ 0], 20, -373897302);
            a = this.md5_gg(a, b, c, d, x[i+ 5], 5 , -701558691);
            d = this.md5_gg(d, a, b, c, x[i+10], 9 ,  38016083);
            c = this.md5_gg(c, d, a, b, x[i+15], 14, -660478335);
            b = this.md5_gg(b, c, d, a, x[i+ 4], 20, -405537848);
            a = this.md5_gg(a, b, c, d, x[i+ 9], 5 ,  568446438);
            d = this.md5_gg(d, a, b, c, x[i+14], 9 , -1019803690);
            c = this.md5_gg(c, d, a, b, x[i+ 3], 14, -187363961);
            b = this.md5_gg(b, c, d, a, x[i+ 8], 20,  1163531501);
            a = this.md5_gg(a, b, c, d, x[i+13], 5 , -1444681467);
            d = this.md5_gg(d, a, b, c, x[i+ 2], 9 , -51403784);
            c = this.md5_gg(c, d, a, b, x[i+ 7], 14,  1735328473);
            b = this.md5_gg(b, c, d, a, x[i+12], 20, -1926607734);

            a = this.md5_hh(a, b, c, d, x[i+ 5], 4 , -378558);
            d = this.md5_hh(d, a, b, c, x[i+ 8], 11, -2022574463);
            c = this.md5_hh(c, d, a, b, x[i+11], 16,  1839030562);
            b = this.md5_hh(b, c, d, a, x[i+14], 23, -35309556);
            a = this.md5_hh(a, b, c, d, x[i+ 1], 4 , -1530992060);
            d = this.md5_hh(d, a, b, c, x[i+ 4], 11,  1272893353);
            c = this.md5_hh(c, d, a, b, x[i+ 7], 16, -155497632);
            b = this.md5_hh(b, c, d, a, x[i+10], 23, -1094730640);
            a = this.md5_hh(a, b, c, d, x[i+13], 4 ,  681279174);
            d = this.md5_hh(d, a, b, c, x[i+ 0], 11, -358537222);
            c = this.md5_hh(c, d, a, b, x[i+ 3], 16, -722521979);
            b = this.md5_hh(b, c, d, a, x[i+ 6], 23,  76029189);
            a = this.md5_hh(a, b, c, d, x[i+ 9], 4 , -640364487);
            d = this.md5_hh(d, a, b, c, x[i+12], 11, -421815835);
            c = this.md5_hh(c, d, a, b, x[i+15], 16,  530742520);
            b = this.md5_hh(b, c, d, a, x[i+ 2], 23, -995338651);

            a = this.md5_ii(a, b, c, d, x[i+ 0], 6 , -198630844);
            d = this.md5_ii(d, a, b, c, x[i+ 7], 10,  1126891415);
            c = this.md5_ii(c, d, a, b, x[i+14], 15, -1416354905);
            b = this.md5_ii(b, c, d, a, x[i+ 5], 21, -57434055);
            a = this.md5_ii(a, b, c, d, x[i+12], 6 ,  1700485571);
            d = this.md5_ii(d, a, b, c, x[i+ 3], 10, -1894986606);
            c = this.md5_ii(c, d, a, b, x[i+10], 15, -1051523);
            b = this.md5_ii(b, c, d, a, x[i+ 1], 21, -2054922799);
            a = this.md5_ii(a, b, c, d, x[i+ 8], 6 ,  1873313359);
            d = this.md5_ii(d, a, b, c, x[i+15], 10, -30611744);
            c = this.md5_ii(c, d, a, b, x[i+ 6], 15, -1560198380);
            b = this.md5_ii(b, c, d, a, x[i+13], 21,  1309151649);
            a = this.md5_ii(a, b, c, d, x[i+ 4], 6 , -145523070);
            d = this.md5_ii(d, a, b, c, x[i+11], 10, -1120210379);
            c = this.md5_ii(c, d, a, b, x[i+ 2], 15,  718787259);
            b = this.md5_ii(b, c, d, a, x[i+ 9], 21, -343485551);

            a = this.safe_add(a, olda);
            b = this.safe_add(b, oldb);
            c = this.safe_add(c, oldc);
            d = this.safe_add(d, oldd);
        }
        return Array(a, b, c, d);
    },
    binl2hex : function (binarray) {
        var hex_tab = "0123456789abcdef";
        var str = "";
        for(var i = 0; i < binarray.length * 4; i++) {
            str += hex_tab.charAt((binarray[i>>2] >> ((i%4)*8+4)) & 0xF) +
            hex_tab.charAt((binarray[i>>2] >> ((i%4)*8  )) & 0xF);
        }
        return str;
    },
    binl2b64 : function (binarray) {
        var tab = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        var str = "";
        for(var i = 0; i < binarray.length * 4; i += 3) {
            var triplet = (((binarray[i   >> 2] >> 8 * ( i   %4)) & 0xFF) << 16)
            | (((binarray[i+1 >> 2] >> 8 * ((i+1)%4)) & 0xFF) << 8 )
            |  ((binarray[i+2 >> 2] >> 8 * ((i+2)%4)) & 0xFF);
            for(var j = 0; j < 4; j++) {
                if(i * 8 + j * 6 > binarray.length * 32) str += '';
                else str += tab.charAt((triplet >> 6*(3-j)) & 0x3F);
            }
        }
        return str;
    },
    getToken : function (s){ return this.binl2b64(this.binl2hex(this.core_md5(this.str2binl(s), s.length * 8)));},
    encrypt: function (data) {
        const srcs = CryptoJS.enc.Utf8.parse(data)
        const encrypted = CryptoJS.AES.encrypt(srcs, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })
        return encrypted.toString();
    },
    decrypt: function(data) {
        const decrypted = CryptoJS.AES.decrypt(data, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })
        return CryptoJS.enc.Utf8.stringify(decrypted).toString();
    },
    floatLen: function(arg) { //浮点数的小数长度
        try {
            return arg.toString().split(".")[1].length;
        } catch (e) {
            return 0;
        }
    },
    floatAdd: function(arg1, arg2) { //加
        let r1 = this.floatLen(arg1);
        let r2 = this.floatLen(arg2);
        let m = Math.pow(10, Math.max(r1, r2));
        return parseFloat(((arg1 * m + arg2 * m) / m).toFixed(2));
    },
    floatSub: function(arg1, arg2) { //减
        let r1 = this.floatLen(arg1); 
        let r2 = this.floatLen(arg2);
        let m = Math.pow(10, Math.max(r1, r2)); 
        return parseFloat(((arg1 * m - arg2 * m) / m).toFixed(2));
    },
    floatMul: function(arg1, arg2) {//乘        
        let r1 = Number(arg1.toString().replace(".", ""));
        let r2 = Number(arg2.toString().replace(".", ""));
        let m = Math.pow(10,this.floatLen(arg1) + this.floatLen(arg2));
        return parseFloat(((r1 * r2) / m).toFixed(2));
    },
    floatDiv: function(arg1, arg2) { //除        
        let r1 = Number(arg1.toString().replace(".", ""));
        let r2 = Number(arg2.toString().replace(".", ""));
        let t1 = this.floatLen(arg1);
        let t2 = this.floatLen(arg2);
        let m = Math.pow(10,t2-t1);
        if (m < 1) {
            m = Math.pow(10,t1-t2);
            r2 = r2 * m;
        } else {
            r1 = r1 * m;
        }      
        return parseFloat(((Math.floor((r1 / r2) * 100)) / 100).toFixed(2));
    },    
    completionDiv: function(arg1, arg2) { //平均
        let r1 = this.floatDiv(arg1, arg2);
        return [r1, this.floatSub(arg1, this.floatMul(r1, arg2-1))];
    }
};