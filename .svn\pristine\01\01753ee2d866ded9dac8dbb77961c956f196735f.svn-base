{"_args": [["nodemailer-fetch@1.6.0", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "nodemailer-fetch@1.6.0", "_id": "nodemailer-fetch@1.6.0", "_inBundle": false, "_integrity": "sha1-ecSQihwPXzdbc/6IjamCj23JY6Q=", "_location": "/nodemailer/mailcomposer/buildmail/nodemailer-fetch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "nodemailer-fetch@1.6.0", "name": "nodemailer-fetch", "escapedName": "nodemailer-fetch", "rawSpec": "1.6.0", "saveSpec": null, "fetchSpec": "1.6.0"}, "_requiredBy": ["/nodemailer/mailcomposer/buildmail"], "_resolved": "https://registry.npm.taobao.org/nodemailer-fetch/download/nodemailer-fetch-1.6.0.tgz", "_spec": "1.6.0", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/nodemailer/nodemailer-fetch/issues"}, "description": "GET HTTP contents", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.12.7", "mocha": "^3.0.2"}, "homepage": "https://github.com/nodemailer/nodemailer-fetch#readme", "keywords": ["nodemailer", "http"], "license": "MIT", "main": "lib/fetch.js", "name": "nodemailer-fetch", "repository": {"type": "git", "url": "git+https://github.com/nodemailer/nodemailer-fetch.git"}, "scripts": {"test": "grunt mochaTest"}, "version": "1.6.0"}