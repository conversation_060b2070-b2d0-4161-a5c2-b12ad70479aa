const { loggers } = require("winston");

function GameCodeChunTian(majiang, app) {
    this.majiang = majiang;
    this.app = app;
    // this.UseCardsInDatabaseData = null;
    // this.CardDataBaseRecordAry = [];
    // this.firstIndex = -1;//每局先手玩家
    this.firstPlayerPutCount = 0;
}


GameCodeChunTian.prototype.initPlayerOnce = function (pl, tb) {
    pl.roomStatistics = [0, 0, 0, 0, 0, 0];    //房间统计数据
}

GameCodeChunTian.prototype.printCallStack = function (count) {
    var caller = arguments.callee.caller;
    var i = 0;
    count = count || 10;
    logger.debug("***----------------------------------------  ** " + (i + 1));
    while (caller && i < count) {
        logger.debug(caller.toString());
        caller = caller.caller;
        i++;
        logger.debug("***---------------------------------------- ** " + (i + 1));
    }
}

GameCodeChunTian.prototype.initAreaSelectMode = function (tb) {
    var tData = tb.tData;
    // 直接对齐前端参数
    tData.areaSelectMode = {
        gameType: tb.createParams.gameType,
        jushu: tb.createParams.jushu,
        maxPlayer: tb.createParams.maxPlayer || tb.createParams.playerNum || 3,
        wanfa: tb.createParams.wanfa,
        mustPut:false,
        rules: tb.createParams.rules || [],
        difen: tb.createParams.difen || 1
    };
    // 规则多选布尔化
    const ruleList = ["ziranchun", "shachun", "sandaiyi", "sandaier", "sidaiyi", "sidaier", "youdabuda", "fanchun", "baodanbida", "4A"];
    ruleList.forEach(rule => {
        tData.areaSelectMode[rule] = tData.areaSelectMode.rules.indexOf(rule) !== -1;
    });
    tData.areaSelectMode.mustPut=tData.areaSelectMode.youdabuda;
};

GameCodeChunTian.prototype.initPlayer = function (pl) {
    pl.zhaDanCount = 0;             // 炸弹数量
    pl.zhaDanScore = 0;             // 结算后的炸弹分
    pl.noPutMaxInBaoDan = false;    // 下家报单时没出最大的牌
    pl.isFangQiangGuan = false;          // 是否关春天
    pl.mjput = [];
    pl.mjhandRecord = [];           // 起手的牌
    pl.mjdesc = [];
    pl.mjdesc1 = [];
    pl.mjdesc2 = [];
    //岳阳跑得快使用下面，以后会同步
    pl.mjdesc0 = [];
    pl.mjdescScore = [];
    pl.mjdesc3 = [];  // 大小关
    pl.winone2 = 0;
    pl.winall2 = 0;
    pl.roundPutCount = 0;
};

// runStartGame 中执行
GameCodeChunTian.prototype.doBeforeHands = function (tb) {
    var tData = tb.tData;

    var handCardNum = 0 == tData.areaSelectMode['cardNumIndex'] ? 10 : 10;
    tData.handCount = handCardNum;
    tData.lastPutPlayer = -1;
    tData.hadTipCards = false;
    tData.actionRecord = [];    // 记录一回合最后3个操作， 过牌/出牌   [{id, mjput}, {id, mjput} ...]
    tData.allActionRecord = []; // 记录所有回合的操作 [回合1=actionRecord, 回合2=actionRecord ...]
    // 金币场记录所有打出的牌
    if (tb.createParams.fieldId)
        tData.allPutCards = [];

    this.majiang.handCount = handCardNum;


    tData.UseCardsInDatabaseData = null;
    tData.firstIndex = -1;//每局先手玩家
    // tData.CardDataBaseRecordAry = [];

    // 默认飘分
    if (tData.areaSelectMode.piaofen == 4) {
        tb.AllPlayerRun(function (p) {
            p.jiazhuNum = 1;
        });
    } else if (tData.areaSelectMode.piaofen == 5) {
        tb.AllPlayerRun(function (p) {
            p.jiazhuNum = 2;
        });
    }

    tb.AllPlayerRun((p) => {
        if (!p.trustWholeTimes || p.trustWholeTimes <= 0) {
            p.trustWholeTimes = [1, 2, 100][tb.tData.areaSelectMode.trustWay || 0];
        }
    });
}

var v = function (card) {
    return (Math.ceil(card / 4) + 10) % 13 + 1;
}

GameCodeChunTian.prototype.calCounts = function (cards) {
    var counts = new Array(14).fill(0);
    for (var i = 0; i < cards.length; i++) {
        counts[v(cards[i])]++;
    }
    return counts;
}

// 剩余牌统计
GameCodeChunTian.prototype.calLeftCards = function (hands, putCardsRecord, extra) {
    var leftCards = new Array(14).fill(4);
    leftCards[13] = 1;
    leftCards[12] = 3;
    if (extra && extra.allHands) {
        extra.count1 = this.calCounts(extra.allHands[(extra.myPos + 1) % 3]);
        extra.count2 = this.calCounts(extra.allHands[(extra.myPos + 2) % 3]);
        for (var i = 0; i < extra.count1.length; i++) {
            leftCards[i] = Math.max(extra.count1[i], extra.count2[i]);
        }
    }
    else {
        for (var i = 0; i < hands.length; i++) {
            leftCards[v(hands[i])]--;
        }
        for (var i = 0; i < putCardsRecord.length; i++) {
            for (var j = 0; j < putCardsRecord[i].length; j++) {
                leftCards[v(putCardsRecord[i][j])]--;
            }
        }
    }
    return leftCards;
}

GameCodeChunTian.prototype.disparityScore = function (tb, targetUid, spadeThree) {
    var tData = tb.tData;
    var score = 2;
    if (tData.areaSelectMode["firstHeiTao3"] && spadeThree) {
        score = 1;
    }
    else if (targetUid == tData.uids[(tData.zhuang + tData.maxPlayer) % tData.maxPlayer]) {
        score = 1;
    }
    return score;
}

//手牌评分函数
GameCodeChunTian.prototype.calculateScore = function (oHands, areaSelectMode, pokerInfo, tb) {
    var cards = oHands.slice();
    var leftCards = this.calLeftCards(cards, [], pokerInfo);
    var enemyMaxValues = tb.pokerInfo.calAllTypeMaxValue(leftCards);
    pokerInfo.leftCards = leftCards;
    var splitRes = [];
    tb.pokerInfo.splitCards(cards, splitRes, enemyMaxValues, pokerInfo);
    return splitRes[0].score;
}


//option =   0b0001(执行第一种策略)
//           0b0010(执行第二种策略)
//           0b0100(执行第三种策略)
//           0b1000(执行第三种策略)
GameCodeChunTian.prototype.luckyPlayer_paodekuaiTY = function (tb, oprtion) {
    logger.debug("===========luckyPlayer_paodekuaiTY============")
    //首局不走次逻辑，避免黑桃三被换掉
    if (tb.tData.roundAll == tb.tData.roundNum || tb.tData.areaSelectMode.isPreRoundFirstRule) return;
    logger.debug("*****++++幸运玩家逻辑========开始");
    var _this = this;
    var plAry_Zhadan = [];
    var plAry_Grade = [];

    var OtherCards_Zhadan = this.majiang.ZhadanCount(tb.tData.otherCards, tb.tData.areaSelectMode);
    //统计桌上玩家的炸弹数量
    tb.AllPlayerRun(function (p) {
        plAry_Zhadan.push(
            {
                player: p,
                zhadanCnt: _this.majiang.ZhadanCount(p.mjhand, tb.tData.areaSelectMode),
            });
    });

    //按炸弹数量降序
    plAry_Zhadan.sort(function (a, b) {
        return b.zhadanCnt - a.zhadanCnt;
    });
    //按幸运值降序
    plAry_Grade = plAry_Zhadan.slice();
    plAry_Grade.sort(function (a, b) {
        return b.player.info.grade.common - a.player.info.grade.common;
    });


    if (plAry_Zhadan.length < 2 || plAry_Grade.length < 2)
        return;

    //*****************日志begin**********************************
    logger.debug("剩余牌：" + tb.tData.otherCards + "====" + OtherCards_Zhadan + "=" + tb.tData.areaSelectMode['cardNumIndex']);
    for (var i = 0; i < plAry_Zhadan.length; i++) {
        var item = plAry_Zhadan[i];
        logger.debug("[playIdZhadan:" + item.player.uid + "]" + "[zhadanCnt:" + item.zhadanCnt + "]" + "[grade:" + item.player.info.grade.common + "]" + "[zhadanCnt:" + item.player.mjhand + "]");
    }
    for (var i = 0; i < plAry_Grade.length; i++) {
        var item = plAry_Grade[i];
        logger.debug("[playIdZhadan:" + item.player.uid + "]" + "[zhadanCnt:" + item.zhadanCnt + "]" + "[grade:" + item.player.info.grade.common + "]" + "[zhadanCnt:" + item.player.mjhand + "]");
    }
    //*****************日志end**********************************;

    function rnd(n, m) {
        var random = Math.floor(Math.random() * (m - n + 1) + n);
        return random;
    }

    logger.debug("===============", oprtion)
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////    
    //首先处理拆炸弹
    // · 若所有手牌（二人玩则包括没人打的拿手牌）的炸弹总数>=1
    // · 符合前一个条件，50%概率进行处理
    // · 随机选择一个炸弹，抽掉一张和另一手牌里的一张交换(此条废弃)
    // . 抽掉两张和另两手牌各交换一张
    //新增
    // 1、将该逻辑也对15张玩法生效，即不再限于16张、而是对整个湖南app的跑得快玩法生效
    // 2、 扩充处理条件
    // - 若炸弹总数=1，30%处理
    // - 若炸弹总数>=2，100%处理
    // 3、增加当多人手上都有炸弹时
    // - 按累积分从高到低的优先级处理
    var zdCnt = 0;
    for (var i = 0; i < plAry_Zhadan.length; i++)
        zdCnt += plAry_Zhadan[i].zhadanCnt
    zdCnt += OtherCards_Zhadan;
    if ((plAry_Zhadan[0].zhadanCnt >= 1 || OtherCards_Zhadan >= 1) && zdCnt > 0 && rnd(1, 10) <= (zdCnt > 1 ? 10 : 3) /*&& tb.tData.areaSelectMode['cardNumIndex'] == 0*/ && (oprtion & 1) > 0) {
        var zdAy = [];
        var maxClubG = -1;
        var maxIndex = -1;
        for (var i = 0; i < plAry_Zhadan.length; i++) {
            if (plAry_Zhadan[i].zhadanCnt >= 1)
                zdAy.push(i);
            if (plAry_Zhadan[i].zhadanCnt >= 1 &&
                plAry_Zhadan[i].player.clubGradeValue &&
                plAry_Zhadan[i].player.clubGradeValue > 0 &&
                plAry_Zhadan[i].player.clubGradeValue > maxClubG) {
                maxClubG = plAry_Zhadan[i].player.clubGradeValue;
                maxIndex = i;
            }
        }

        //找出拆除炸弹和被交换的牌组
        var ZDNum = rnd(0, zdAy.length - (OtherCards_Zhadan >= 1 ? 0 : 1))
        ZDNum = ZDNum >= zdAy.length ? 2 : zdAy[ZDNum];
        if (maxIndex >= 0) ZDNum = maxIndex;
        //logger.debug('chai_player_id', plAry_Zhadan[ZDNum].player.uid);
        var destNum1 = (ZDNum + 1) % 3;
        var destNum2 = (ZDNum + 2) % 3;
        var ZDCntScr = ZDNum >= plAry_Zhadan.length ? OtherCards_Zhadan : plAry_Zhadan[ZDNum].zhadanCnt;//被拆炸弹牌组的炸弹个数
        var pkAryZD = ZDNum >= plAry_Zhadan.length ? tb.tData.otherCards : plAry_Zhadan[ZDNum].player.mjhand;//被拆炸弹牌组
        var pkAryDest1 = destNum1 >= plAry_Zhadan.length ? tb.tData.otherCards : plAry_Zhadan[destNum1].player.mjhand;//被换牌的牌组1
        var pkAryDest2 = destNum2 >= plAry_Zhadan.length ? tb.tData.otherCards : plAry_Zhadan[destNum2].player.mjhand;//被换牌的牌组2

        //找出交换牌的序号
        if (pkAryZD && pkAryZD.length > 0 && pkAryDest1 && pkAryDest1.length > 0 && pkAryDest2 && pkAryDest2.length > 0) {
            pkAryZD.sort();
            var idxDest1 = rnd(0, pkAryDest1.length - 1);
            var idxDest2 = rnd(0, pkAryDest2.length - 1);

            var idxZD = rnd(1, ZDCntScr);//被拆的是第几个炸弹
            var tmpcnt = 1
            for (var i = 0; i < pkAryZD.length; i++) {
                if (this.majiang.isChaiZhaDan(pkAryZD, [pkAryZD[i]], tb.tData.areaSelectMode)) {
                    if (tmpcnt == idxZD) {
                        idxZD = i;
                        break;
                    }
                    tmpcnt++;
                }
            }
            //交换 
            logger.debug("拆炸弹交换= " + pkAryDest1[idxDest1] + " 与 " + pkAryZD[idxZD]);
            var tmpcard = pkAryDest1[idxDest1];
            pkAryDest1[idxDest1] = pkAryZD[idxZD];
            pkAryZD[idxZD] = tmpcard;

            logger.debug("拆炸弹交换= " + pkAryDest2[idxDest2] + " 与 " + pkAryZD[idxZD + 1]);
            var tmpcard = pkAryDest2[idxDest2];
            pkAryDest2[idxDest2] = pkAryZD[idxZD + 1];
            pkAryZD[idxZD + 1] = tmpcard;


            //从新计算
            OtherCards_Zhadan = this.majiang.ZhadanCount(tb.tData.otherCards, tb.tData.areaSelectMode);
            for (var i = 0; i < plAry_Zhadan.length; i++) {
                plAry_Zhadan[i].zhadanCnt = this.majiang.ZhadanCount(plAry_Zhadan[i].player.mjhand, tb.tData.areaSelectMode);
            }
            plAry_Zhadan.sort(function (a, b) { return b.zhadanCnt - a.zhadanCnt; });
        }
    }
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////
    //再处理2人幸运逻辑
    if (tb.tData.otherCards && plAry_Zhadan.length == 2 &&
        plAry_Zhadan[0].player.mjhand.length == tb.tData.otherCards.length &&
        plAry_Zhadan[1].player.mjhand.length == tb.tData.otherCards.length &&
        (oprtion & 2) > 0) {
        // 2人场中，两个玩家的手牌A、B，剩余的牌C
        // 1、判断A、B两手牌的炸弹数，将其中更大值与C的炸弹输比较；若A、B炸弹数相等，则随机选择一个
        // 2、 若大于C的炸弹数，则将这手牌与C交换
        var _2ren_MaxZD = 0;
        if (plAry_Zhadan[0].zhadanCnt == plAry_Zhadan[1].zhadanCnt) _2ren_MaxZD = rnd(0, 1);
        if (plAry_Zhadan[_2ren_MaxZD].zhadanCnt > OtherCards_Zhadan) {
            //交换 plAry_Zhadan[_2ren_MaxZD] 和 tb.tData.otherCards
            logger.debug("playid=" + plAry_Zhadan[_2ren_MaxZD].player.uid + " 与 剩余牌进行交换");
            var tmpCards = tb.tData.otherCards;
            tb.tData.otherCards = plAry_Zhadan[_2ren_MaxZD].player.mjhand;
            plAry_Zhadan[_2ren_MaxZD].player.mjhand = tmpCards;

            //交换炸弹数量
            var tmpZD = plAry_Zhadan[_2ren_MaxZD].zhadanCnt;
            plAry_Zhadan[_2ren_MaxZD].zhadanCnt = OtherCards_Zhadan;
            OtherCards_Zhadan = tmpZD;

            //发生交换，从新按炸弹数量降序
            plAry_Zhadan.sort(function (a, b) {
                return a.zhadanCnt - b.zhadanCnt;
            })
        }
    }
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////
    //最后处理2人，3人通用幸运逻辑,
    // 每局发牌后，对幸运值最大的一个玩家,当其幸运值>50时
    // 1）判断其手牌的炸弹数是否最多（二人玩时，包括没人打的那手牌）
    // 2）若不是最多，则将其手牌与炸弹最多的手牌交换
    // 3）若发生交换，则该玩家的幸运值-50；注意被换牌者的幸运值不变
    //如果幸运值相等，随机看要不要交换50%的几率
    if ((plAry_Grade[0].zhadanCnt < plAry_Zhadan[0].zhadanCnt || plAry_Grade[0].zhadanCnt < OtherCards_Zhadan) &&
        plAry_Grade[0].player.info.grade.common > 50 && (oprtion & 4) > 0) {
        if (plAry_Zhadan[0].zhadanCnt > OtherCards_Zhadan) {//玩家之间交换
            var b = (plAry_Grade[0].player.info.grade.common == plAry_Zhadan[0].player.info.grade.common) ? (rnd(0, 1) == 1) : true;
            if (b) {//交换 plAry_Grade[0] 和 plAry_Zhadan[0]的手牌
                logger.debug("playid=" + plAry_Grade[0].player.uid + " 与 " + "playid=" + plAry_Zhadan[0].player.uid + "发生交换");
                var tmpCards = plAry_Grade[0].player.mjhand;
                plAry_Grade[0].player.mjhand = plAry_Zhadan[0].player.mjhand;
                plAry_Zhadan[0].player.mjhand = tmpCards;
                plAry_Grade[0].player.info.grade.common -= 50;
                //小于0置0
                plAry_Grade[0].player.info.grade.common = plAry_Grade[0].player.info.grade.common < 0 ? 0 : plAry_Grade[0].player.info.grade.common;
            }
        }
        else {//和底牌交换
            //交换 plAry_Grade[0] 和 tb.tData.otherCards
            logger.debug("playid=" + plAry_Grade[0].player.uid + " 与 剩余牌进行交换");
            var tmpCards = tb.tData.otherCards;
            tb.tData.otherCards = plAry_Grade[0].player.mjhand;
            plAry_Grade[0].player.mjhand = tmpCards;
            plAry_Grade[0].player.info.grade.common -= 50;
            //小于0置0
            plAry_Grade[0].player.info.grade.common = plAry_Grade[0].player.info.grade.common < 0 ? 0 : plAry_Grade[0].player.info.grade.common;
        }
    }
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////
    //俱乐部幸运值玩家换牌逻辑
    // 从上到下，三条里面最多执行一个操作
    // 二人玩法从不要的牌里面换，优先换小牌
    // 三人玩法随机找人换，优先换小牌
    // 不同条目换牌不分先后，都把牌抽出后一起换，换回来的牌点数不能相同、不能和抽出的牌有相同
    // ·拆4连或以上连对
    // ·拆7连或以上的顺子
    // ·拆3组或以上的三顺
    // 最后另外有80%概率拆一个炸弹（若有）
    if ((oprtion & 8) > 0) {
        var maxClubGradeUid = 0;
        var maxClubGrade = -1;
        var that = this;
        tb.AllPlayerRun(function (p) {
            logger.debug(p.uid, p.clubGradeValue, p.clubGradeStatus, that.majiang.cardCfg.ht[3])

            if (p.clubGradeStatus && p.clubGradeValue > maxClubGrade) {
                maxClubGradeUid = p.uid;
                maxClubGrade = p.clubGradeValue;
            }
        });

        if (maxClubGradeUid != 0) {
            logger.debug("===============开始俱乐部幸运值换牌逻辑", maxClubGradeUid, maxClubGrade)
            var desUid = tb.tData.maxPlayer == 2 ? null : tb.tData.uids[(tb.tData.uids.indexOf(maxClubGradeUid) + rnd(1, 2)) % tb.tData.maxPlayer];

            var scrHands = tb.players[maxClubGradeUid].mjhand;
            var desHands = desUid == null ? tb.tData.otherCards : tb.players[desUid].mjhand;

            // if ( this.doClubHuanPai(scrHands, desHands,this.majiang.CARDTPYE.liandui, tb)){}
            // else if (this.doClubHuanPai(scrHands, desHands,this.majiang.CARDTPYE.shunzi, tb)){}
            // else if (this.doClubHuanPai(scrHands, desHands,this.majiang.CARDTPYE.sanshun, tb)){}

            if (rnd(1, 10) > 2)//80%的几率走拆炸弹逻辑
            {
                if (this.doClubHuanPai(scrHands, desHands, this.majiang.CARDTPYE.sizha, tb)) { }
                else if (this.doClubHuanPai(scrHands, desHands, this.majiang.CARDTPYE.sange3, tb)) { }
                else if (this.doClubHuanPai(scrHands, desHands, this.majiang.CARDTPYE.sangeA, tb)) { }
            }
        }
    }

    //*****************日志begin**********************************
    for (var i = 0; i < plAry_Zhadan.length; i++) {
        var item = plAry_Zhadan[i];
        logger.debug("[playId:" + item.player.uid + "]" + "[zhadanCnt:" + item.zhadanCnt + "]" + "[grade:" + item.player.info.grade.common + "]" + "[zhadanCnt:" + item.player.mjhand + "]");
    }
    logger.debug("*****++++幸运玩家逻辑========结束");
    //*****************日志end**********************************
};

GameCodeChunTian.prototype.calGrade_paodekuaiTY = function (tb, pl) {
    if (!utils.isContains(['shaoyang', 'yueyang', 'yueyang-test', 'shaoyang-test', 'dev-c'], env)) {
        return;
    }
    var cost = parseInt((pl.isNeedFanBei ? pl.winall / 2 : pl.winall) / (tb.tData.areaSelectMode["difen"] || 1));
    if (pl && pl.info.grade.common) {

        pl.info.grade.common -= cost;
        logger.debug("*****++++calGrade_paodekuaiTY 输分累积11111111", pl.info.grade.common, cost);

    }
    else {
        pl.info.grade.common = -cost;
    }
    pl.info.grade.common = Math.max(0, Math.min(300, pl.info.grade.common));
};

GameCodeChunTian.prototype.doBeforeSendHands = function (tb) {
    var tData = tb.tData;
    for (var i = 0; i < tData.maxPlayer; i++) {
        var pl = tb.players[tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]];
        if (pl.getCard && pl.getCard.length != 0) {
            var cIndex = pl.getCard[0];
            if (cIndex != i) {
                var tmp = pl.mjhand.slice();
                pl.mjhand = tb.players[tData.uids[(cIndex + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]].mjhand;
                tb.players[tData.uids[(cIndex + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]].mjhand = tmp;
            }
        }
    }

};

// runStartGame 前执行 准备后是否有其他操作, 没有则走原流程  runStartGame
GameCodeChunTian.prototype.isDoAfterReady = function (tb) {
    return tb.tData.areaSelectMode['isPlayerShuffle'] == 1;
}

//是否需要飘分
GameCodeChunTian.prototype.isDoPiaofen = function (tb) {
    var piaoFenIndex = tb.tData.areaSelectMode['piaofen'];
    var needPiao = typeof (piaoFenIndex) == "number" && piaoFenIndex > 0 && piaoFenIndex < 4;

    return needPiao;
}

// 所有人准备后， 通知客户端执行 切牌操作
GameCodeChunTian.prototype.doAfterReady = function (tb) {
    if (!this.checkIsAllReady(tb)) {
        logger.debug('doAfterRead checkIsAllReady false')
        return false;
    }

    var tData = tb.tData;

    tData.tState = TableState.afterReady;
    tb.AllPlayerRun(function (p) {
        p.mjState = TableState.afterReady;
    });

    var handCardNum = 0 == tData.areaSelectMode['cardNumIndex'] ? 16 : 15;
    var qiepaiPlayer = 0;
    var isFirstRound = tData.roundAll == tData.roundNum;

    handCardNum = handCardNum * 3;
    if (tData.zhuang >= 0) {
        if (isFirstRound) {
            qiepaiPlayer = tData.zhuang;
        } else {
            qiepaiPlayer = (tData.zhuang + tData.maxPlayer - 1) % tData.maxPlayer;
        }
    }

    var msg = {};
    msg.actionName = 'qiepai';
    msg.curPlayer = qiepaiPlayer;
    msg.shuffleCardsNum = handCardNum;
    msg.tState = TableState.afterReady;

    tData.actionName = 'qiepai';
    tData.curPlayer = qiepaiPlayer
    tData.shuffleCardsNum = handCardNum
    tData.tState = TableState.afterReady
    tb.NotifyAll("after_ready", msg);

    tb.trustWhole();
}

GameCodeChunTian.prototype.doPiaofen = function (tb) {
    var tData = tb.tData;
    tData.tState = TableState.waitJiazhu;
    tb.AllPlayerRun(function (p) {
        p.mjState = TableState.waitJiazhu;
        p.jiazhuNum = -1;
    });
    logger.debug("=====NotifyAll waitJiazhu========");
    tb.NotifyAll("waitJiazhu", {});

    tb.trustWhole();
}

//飘分消息
GameCodeChunTian.prototype.mjJiazhu = function (pl, msg, session, next, tb) {
    logger.debug("GameCodeChunTian.mjJiazhu=====msg.jiazhuNum=====" + msg.jiazhuNum);
    var tData = tb.tData;

    // 不是等待加注的状态， 不能拉庄或顶庄
    // 加注后状态为 isReady, 加注前状态为 waitJiazhu
    if (pl.mjState != TableState.waitJiazhu) return;

    pl.jiazhuNum = Number(msg.jiazhuNum);
    pl.mjState = TableState.isReady;

    var selectedPlayers = tb.FindAllPlayer(function (p) {
        return p.mjState == TableState.isReady;
    })

    if (selectedPlayers.length == tData.maxPlayer) {
        if (this.isDoAfterReady(tb)) {
            this.doAfterReady(tb);
        } else {
            tb.runStartGame();
        }
    } else
        tb.NotifyAll("s2cMJJiazhu", { uid: pl.uid });
};


// 是否需要手动准备
GameCodeChunTian.prototype.isBtnReady = function (tb) {
    return true;
}

GameCodeChunTian.prototype.updateClientScene = function (tb, pl) {
    if (pl) {
        var data = tb.initSceneData(pl);
        if (data && data.players && data.players[pl.uid]) {
            if (data.players[pl.uid].mjhand === null || typeof data.players[pl.uid].mjhand === 'undefined') {
                data.players[pl.uid].mjhand = [];
            }
        }
        pl.notify("initSceneData", data);
    } else {
        tb.AllPlayerRun(function (pl) {
            var data = tb.initSceneData(pl);
            if (data && data.players && data.players[pl.uid]) {
                if (data.players[pl.uid].mjhand === null || typeof data.players[pl.uid].mjhand === 'undefined') {
                    data.players[pl.uid].mjhand = [];
                }
            }
            pl.notify("initSceneData", data);
        }.bind(this));
    }
}

// 设置出牌的人
GameCodeChunTian.prototype.doAfterHands = function (tb) {
    var tData = tb.tData;
    var canHongTao10Niao = tData.areaSelectMode.hongTao10Niao || tData.areaSelectMode.hongTao10JiaFen || tData.areaSelectMode.zhaniao > 1;
    var canHongTao9Niao = tData.areaSelectMode.zhaniao == 1;
    var isFirstRound = tData.areaSelectMode.isPreRoundFirstRule ? true : tData.roundAll == tData.roundNum;
    var firstPutRuleNum = tData.areaSelectMode.firstPutRule;
    var isHeiTao3ShouChu = firstPutRuleNum != 4;

    tData.ht10Player = -1;
    tData.ht9Player = -1;
    tData.lastPutPlayer = -1;

    var allOriginCards;
    if (tb.createParams.fieldId) {
        allOriginCards = this.getLeftCards(tb);
    }

    tb.AllPlayerRun(function (p) {
        p.mjhandRecord = p.mjhand.slice();
        if (isFirstRound && isHeiTao3ShouChu && p.mjhand.indexOf(this.majiang.cardCfg.ht[3]) >= 0) {
            tData.curPlayer = tData.uids.indexOf(p.uid) - 1;
        }
        if (canHongTao10Niao && p.mjhand.indexOf(this.majiang.cardCfg.hx[10]) >= 0) {
            tData.ht10Player = tData.uids.indexOf(p.uid);
        }
        if (canHongTao9Niao && p.mjhand.indexOf(this.majiang.cardCfg.hx[9]) >= 0) {
            tData.ht9Player = tData.uids.indexOf(p.uid);
        }
        if (allOriginCards && tData.jipaiqi && utils.isContains(tData.jipaiqi, p.uid))
            p.notify("initJiPaiQi", { jipaiqi: tData.jipaiqi, leftCards: allOriginCards });
        p.handCount = p.mjhand.length;
    }.bind(this))

    if (isFirstRound && 2 == tData.maxPlayer) {
        if (!isHeiTao3ShouChu) {
            tData.curPlayer = Math.floor(Math.random() * 10) % tData.maxPlayer;
        }
    }
    this.firstPlayerPutCount = 0; // 重置计数器
    tData.firstIndex = (tData.curPlayer + 1) % tData.maxPlayer;
    this.newRoundBegan = true;

    // ====== 检查自然春：发牌后立即检查所有玩家是否有自然春牌型 ======
    for (var i = 0; i < tData.maxPlayer; i++) {
        var player = tb.getPlayer(tData.uids[i]);
        if (this.majiang.checkNaturalSpring(player.mjhand, tData.areaSelectMode)) {
            tData.naturalSpringWinner = player.uid; // 标记自然春赢家

            // 发送自然春动画
            tb.NotifyAll("ziranchun", { uid: player.uid });

            // 设置自然春结算描述
            player.mjdesc.push("自然春");
            tb.AllPlayerRun(p => {
                if (p.uid !== player.uid) {
                    p.mjdesc.push("被自然春");
                }
            });

            // 设置游戏状态为等待动画播放
            tData.tState = TableState.waitPut; // 临时设置状态，防止其他操作

            // 延迟结束游戏，让动画播放完成
            setTimeout(() => {
                this.EndGame(tb, player); // 立即结束本局
            }, 3000); // 3秒延迟，让动画播放完成
            return;
        }
    }

    // ====== 报春玩法：发牌后推送waitBaoChun，进入报春选择阶段 ======
    tData.baoChunStatus = "waiting";
    tData.baoChunChoices = {};
    tData.baoChunPlayer = null;
    tData.isBaoChun = false;
    tData.baoChunFailed = false;
    tData.baoChunWinner = null;
    tData.baoChunSuccess = false;

    // 设置15秒报春选择超时
    if (tData.baoChunTimer) {
        clearTimeout(tData.baoChunTimer);
    }
    tData.baoChunTimer = setTimeout(() => {
        // 15秒后自动处理未选择的玩家为不报春
        this.handleBaoChunTimeout(tb);
    }, 15000);

    // 发送报春状态给客户端，包含倒计时信息
    tb.NotifyAll("waitBaoChun", {
        baoChunStatus: tData.baoChunStatus,
        zhuang: tData.zhuang,
        zhuangUid: tData.uids[tData.zhuang],
        timeLimit: 15 // 15秒时间限制
    });
    return; // 等待报春选择，后续流程在报春选择后继续
};



GameCodeChunTian.prototype.sendNewCard = function (tb, assignPlayerIdx) {
    var tData = tb.tData;

    // 如果正在等待报春选择或庄家确认，不执行发牌逻辑
    if (tData.baoChunStatus === "waiting" || tData.baoChunStatus === "waitingZhuangConfirm") {
        return;
    }

    if (typeof assignPlayerIdx === 'number') {
        tData.curPlayer = assignPlayerIdx;
    } else {
        tData.curPlayer = (tData.curPlayer + 1) % tData.maxPlayer;
    }
    logger.debug("tData.curPlayertData.curPlayertData.curPlayertData.curPlayer======1====", tData.curPlayer);
    tData.tState = TableState.waitPut;

    var pl = tb.getPlayer(tData.uids[tData.curPlayer]);

    // 开局第一次出牌 || 管牌者出牌
    if (tData.lastPutPlayer == -1 || tData.lastPutPlayer == tData.curPlayer) {
        var roundActionRecord = tData.allActionRecord[tData.allActionRecord.length - 1];
        if (roundActionRecord) {
            var passPlayerNum = tData.maxPlayer - 1;
            tData.actionRecord = roundActionRecord.slice();
            for (var i = 0; i < passPlayerNum; i++) tData.actionRecord.pop();    // 最大牌后的过牌操作去掉
            var recordNum = tData.actionRecord.length
            if (tData.actionRecord.length > 3) tData.actionRecord = tData.actionRecord.slice(recordNum - 3, recordNum);
            tData.actionRecord = tData.actionRecord.reverse();
        }

        tData.allActionRecord.push([]); // 开始记录新一回合的数据
        if (tData.lastPutCard != -1 && this.majiang.isZhaDan(tData.lastPutCard, tData.areaSelectMode) && tData.lastPutPlayer == tData.curPlayer) pl.zhaDanCount++;
        tData.lastPutCard = null;
    }

    tb.AllPlayerRun(function (p) {
        if (p.mjState != TableState.roundFinish) {
            p.mjState = TableState.waitPut;
        }
    });

    var tipscards = []
    if (tData.lastPutCard) {
        tipscards = this.majiang.tipCards(pl.mjhand, tData.lastPutCard, tData.areaSelectMode);
    }

    var haveTip = tipscards.length > 0;
    var isFirstPl = tData.lastPutPlayer == -1
    var isRoundMe = tData.lastPutPlayer == tData.curPlayer;

    if (haveTip || isFirstPl || isRoundMe || tData.areaSelectMode.mustPut == false) {
        tData.hadTipCards = true;
        tb.NotifyAll("waitPut", tData);
        cloneDataAndPush(tb.mjlog, "waitPut", tData);

        tb.doTrust(pl);

    } else {
        var delayTime = 0.5
        var speed = tData.areaSelectMode.playSpeed;
        if (speed == 0) {  // 快
            delayTime = 0.2
        }
        else if (speed == 1) {  // 中
            delayTime = 0.5
        }
        else if (speed == 2) {  // 慢
            delayTime = 1.0
        }
        if (pl.pdk_pass_timeout) {
            clearTimeout(pl.pdk_pass_timeout);
            pl.pdk_pass_timeout = null;
        }
        pl.pdk_pass_timeout = setTimeout(function () {
            tData.hadTipCards = false;
            logger.debug("tData.curPlayertData.curPlayertData.curPlayertData.curPlayer==========", tData.curPlayer);
            tb.NotifyAll("waitPut", tData);
            cloneDataAndPush(tb.mjlog, "waitPut", tData);
            this.doPassCard(pl, tb, 10)//服务器自动过
        }.bind(this), delayTime * 1000);
    }

    this.newRoundBegan = false;
}

GameCodeChunTian.prototype.getNextOffPlayer = function (plIndex, tb, off) {
    var tData = tb.tData;
    off = off || 1;
    return tb.getPlayer(tData.uids[(tData.maxPlayer + plIndex + off) % tData.maxPlayer]);
}


// 玩家手动选额切牌位置 
GameCodeChunTian.prototype.c2sPlayerShuffleIndex = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState == TableState.afterReady && pl.uid == tData.uids[tData.curPlayer]) {
        tb.NotifyAll("select_shuffle_index", msg);
    }
}

// 玩家完成手动切牌 
GameCodeChunTian.prototype.c2sPlayerShuffleFinish = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState == TableState.afterReady) {
        var dealyTime = msg.dealyTime || 2000;
        var pmsg = {};
        pmsg.actionName = 'qiepai_end';
        pmsg.tState = TableState.afterReady;
        tb.NotifyAll("after_ready", pmsg);
        tData.curPlayer = tData.zhuang;
        tData.tState = TableState.waitCard;
        setTimeout(function () {
            tb.runStartGame();
        }, dealyTime);
    }
}

GameCodeChunTian.prototype.MJGetCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.uids.indexOf(pl.uid) == -1) {
        return;
    }
    pl.getCard = [];
    pl.getCard.push(msg.card);
};

GameCodeChunTian.prototype.pkShowCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    tb.next_cards = this.majiang.randomCards(tData.areaSelectMode, tData, tb, true);
    var cardNext = 0;
    var arr = [];
    if (pl.isAuth) {
        for (var i = 0; i < tData.maxPlayer; i++) {
            var str = {};
            var mjhand = [];
            for (var j = 0; j < this.majiang.handCount; j++) {
                mjhand.push(tb.next_cards[cardNext++]);
            }
            str.card = mjhand;
            str.nickname = i;
            arr.push(str);
        }
    }
    pl.notify("pkShowCard", { "info": arr, isAuth: pl.isAuth });

    // var arr = [];
    // if(pl.isAuth){
    //     tb.AllPlayerRun(function (p) {
    //         var str = {};
    //         if(p !=pl){
    //             str.card = p.mjhand;
    //             str.nickname = p.info.nickname;
    //             str.uid=p.uid;
    //             arr.push(str);
    //         }
    //     })
    // }
    // pl.notify("pkShowCard",{"info":arr,isAuth:pl.isAuth});
}

//出
GameCodeChunTian.prototype.pkPutCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;
    pl.roundPutCount++;
    const nonFirstIndex = (tData.curPlayer + 1) % tData.maxPlayer;
    const nonFirstPlayerId = tData.uids[nonFirstIndex];
    logger.debug(`下一家出牌玩家ID: ${nonFirstPlayerId}`);
    if (pl.uid === tData.uids[tData.firstIndex]) {
        this.firstPlayerPutCount++; // 先手玩家出牌次数+1
        logger.debug(`先手玩家 ${pl.uid} 出牌，当前出牌次数: ${this.firstPlayerPutCount}`);
    }
    // 报春胜负判定
    if (tData.isBaoChun && tData.baoChunPlayer) {
        if (pl.uid !== tData.baoChunPlayer && !tData.baoChunFailed) {
            // 非报春玩家首次出牌，报春失败
            tData.baoChunFailed = true;
            tData.baoChunWinner = pl.uid; // 首个出牌的玩家
        }
        if (pl.uid === tData.baoChunPlayer && pl.mjhand.length === msg.card.length && !tData.baoChunFailed) {
            // 报春玩家出完所有牌，报春成功
            tData.baoChunSuccess = true;
            tData.baoChunWinner = pl.uid;
        }
    }
    if (tData.tState == TableState.waitPut && pl.uid == tData.uids[tData.curPlayer]) {
        var nextPlayer = this.getNextOffPlayer(tData.curPlayer, tb, 1);
        var isNextPlayerOneCard = nextPlayer.handCount == 1;
        var isFirstRound = tData.roundAll == tData.roundNum;

        // ====== 报单必打规则：当下家只有一张牌时，如果出单牌只能出最大单牌 ======
        if (isNextPlayerOneCard && tData.areaSelectMode.baodanbida) {
            var cardType = this.majiang.calType(msg.card, tData.areaSelectMode);

            // 只限制单牌，其他牌型（对子、三张、炸弹等）不限制
            if (cardType === this.majiang.CARDTPYE.danpai) {
                var maxCard = this.majiang.getMaxDanPai(pl.mjhand);
                var maxValue = this.majiang.calPoint(maxCard);
                var putValue = this.majiang.calPoint(msg.card[0]);

                if (putValue < maxValue) {
                    console.log("报单必打：出单牌时必须出最大单牌，当前出牌:", putValue, "最大单牌:", maxValue);
                    pl.notify('putCardError', {
                        error: 'baodanbida_must_max_single',
                        message: '下家报单时，出单牌必须出最大单牌',
                        maxCard: maxCard
                    });
                    return; // 拒绝出牌
                }
            }
            // 其他牌型（对子、三张、炸弹等）不受限制，继续正常验证
        }

        var putResult = this.majiang.checkPut(pl.mjhand, msg.card, tData.lastPutCard, tData.areaSelectMode, isNextPlayerOneCard, isFirstRound);
        logger.debug('【DEBUG】checkPut返回:', putResult, '原手牌:', pl.mjhand, '打出:', msg.card);
        if (Array.isArray(putResult)) {
            msg.uid = pl.uid;
            pl.mjhand = putResult;
            pl.handCount = pl.mjhand.length;
            pl.mjput = msg.card.slice();
            msg.handCount = pl.handCount;
            tData.lastPutCard = msg.card.slice();
            tData.lastPutPlayer = tData.curPlayer;

            // 确保同步到前端的 mjhand 不为 null
            msg.mjhand = pl.mjhand || [];
            console.log('mjhand6:' + pl.mjhand);


            var oneActionRecord = { id: pl.uid, mjput: msg.card.slice() };
            tData.allActionRecord[tData.allActionRecord.length - 1].push(oneActionRecord)

            // 金币场记录打出的牌
            if (tb.createParams.fieldId) {
                for (var i = 0; i < msg.card.length; i++) {
                    tData.allPutCards.push(msg.card[i]);
                }
            }

            var cmd = msg.cmd;
            delete msg.cmd;
            logger.debug("pkPutCard=====玩家出牌了======");


            // 出单牌时，如果报单, 判断是否出了最大的牌
            var type = this.majiang.calType(msg.card, tData.areaSelectMode);//计算牌型
            if (type == this.majiang.CARDTPYE.danpai && isNextPlayerOneCard) {
                var maxCard = this.majiang.getMaxDanPai(pl.mjhand);
                var maxValue = this.majiang.calPoint(maxCard);
                var putValue = this.majiang.calPoint(msg.card[0]);
                if (putValue < maxValue) {
                    pl.noPutMaxInBaoDan = true;
                } else {
                    pl.noPutMaxInBaoDan = false;
                }
            }

            tb.NotifyAll(cmd, msg);
            cloneDataAndPush(tb.mjlog, cmd, msg);
                // 若报春胜负已定，直接结束本局
                if (tData.isBaoChun && tData.baoChunPlayer && (tData.baoChunSuccess || tData.baoChunFailed)) {
                    this.EndGame(tb, tb.players[tData.baoChunWinner]);
                   return;
                }
            
            if (pl.mjhand.length == 0 && tData.winner == -1) { // 第一个人打完，游戏结束
                if (this.majiang.isZhaDan(tData.lastPutCard, tData.areaSelectMode)) pl.zhaDanCount++;
                tData.winner = tData.uids.indexOf(pl.uid);
                pl.roomStatistics[0]++; // 大结算 胜利次数
                this.EndGame(tb, pl);
            }
            else {
                this.sendNewCard(tb);
            }
        } else {
            logger.error("服务器不让出牌=", pl.mjhand, msg.card, tData.lastPutCard, tData.areaSelectMode, isNextPlayerOneCard, isFirstRound);
            pl.notify('initSceneData', tb.initSceneData(pl));
        }
    }
    else {
        logger.error("========服务器判断出牌状态有错误============", __filename, tData.gameType, tData.gameCnName);
        pl.notify('initSceneData', tb.initSceneData(pl));
    }
};

GameCodeChunTian.prototype.doPassCard = function (pl, tb, opt) {
    var tData = tb.tData;

    if (pl.pdk_pass_timeout) {
        clearTimeout(pl.pdk_pass_timeout);
        pl.pdk_pass_timeout = null;
    }

    pl.mjput = [];
    var oneActionRecord = { id: pl.uid, mjput: [] };
    tData.allActionRecord[tData.allActionRecord.length - 1].push(oneActionRecord)

    tb.NotifyAll("PKPass", { mjState: pl.mjState, uid: pl.uid, Opt: opt });
    cloneDataAndPush(tb.mjlog, "PKPass", { mjState: pl.mjState, uid: pl.uid, Opt: opt });
    this.sendNewCard(tb);
    // tb.doTrust(pl);

    // 选择了防强关， 且是3人玩 且一张牌没打出
    if (tData.areaSelectMode['fangQiangGuan'] && 3 == tData.maxPlayer && pl.mjhand.length == tData.handCount && pl.isFangQiangGuan == false) {
        var plIndex = tData.uids.indexOf(pl.uid);
        var lastPlayer = this.getNextOffPlayer(plIndex, tb, -1);
        var nextPlayer = this.getNextOffPlayer(plIndex, tb, 1);

        if (lastPlayer.mjput.length > 0 && nextPlayer.mjput.length > 0) {

            var lastTipCards = this.majiang.tipCards(pl.mjhand, lastPlayer.mjput, tData.areaSelectMode);
            var nextTipCards = this.majiang.tipCards(pl.mjhand, nextPlayer.mjput, tData.areaSelectMode);
            // 可以压上上家的牌， 但被上家关了， 出发 防关春天
            if (nextTipCards.length > 0 && lastTipCards.length == 0) {
                var mjHandTmp = this.majiang.sortHandCards(lastPlayer.mjhand.concat(lastPlayer.mjput), 1);
                var lastTipCardsTmp = this.majiang.tipCards(mjHandTmp, nextPlayer.mjput, tData.areaSelectMode);

                //上家出的当前牌型的最小值是否大于我的最大值
                var isFangQiangGuan = false;
                for (var i = 0; i < nextTipCards.length; i++) {
                    for (var j = 0; j < lastTipCardsTmp.length; j++) {
                        if (this.majiang.canPut(nextTipCards[i], lastTipCardsTmp[j], nextTipCards[i].length + 1, tData.areaSelectMode)) {
                            isFangQiangGuan = true;
                            break;
                        }
                    }
                    if (isFangQiangGuan) break;
                }
                if (isFangQiangGuan == false)
                    return;

                //上家是否出的当前牌型的最小值
                var isputSmallest = true;
                for (var i in lastTipCardsTmp) {
                    if (this.majiang.canPut(lastPlayer.mjput, lastTipCardsTmp[i], lastPlayer.mjput.length + 1, tData.areaSelectMode)) {
                        isputSmallest = false;
                        break;
                    }
                }

                if (isputSmallest == false)//上家出的当前牌型的不是最小值
                    pl.isFangQiangGuan = true;
            }
        }
    }
}

GameCodeChunTian.prototype.checkIsAllReady = function (tb) {
    var isAllready = true;
    var tData = tb.tData;

    if (tb.PlayerCount() != tData.maxPlayer) {
        logger.debug("checkIsAllReady=====人未满======");
        return false;
    }

    tb.AllPlayerRun(function (p) {
        if (p.mjState != TableState.isReady) isAllready = false;;
    })

    return isAllready;

}

// 每局播完金币动画之后自动准备
GameCodeChunTian.prototype.pkPassCard = function (pl, msg, session, next, tb) {
    var tData = tb.tData;

    console.log("pkPassCard=====开始算分=====", pl);
    var isFirstPutPl = tData.lastPutPlayer == -1
    var isCurPlayer = pl.uid == tData.uids[tData.curPlayer];
    var isLastPutPl = pl.uid == tData.uids[tData.lastPutPlayer]
    var isEndGame = tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish;
    var isReadyGame = tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady;

    if (tData.tState == TableState.waitPut && !isFirstPutPl && isCurPlayer && !isLastPutPl) {
        // if(!tData.lastPutCard) {
        //     this.updateClientScene(tb, pl);
        //     return;
        // }

        var tipscards = this.majiang.tipCards(pl.mjhand, tData.lastPutCard, tData.areaSelectMode);

        if (tipscards && tipscards.length > 0) {
            if (tData.areaSelectMode.mustPut != false) {
                logger.debug("pkPassCard=====必管且有牌出不能过======");
                return;
            }

            if (msg.autoPass) {
                logger.debug("pkPassCard=====前端自动请求过牌时 有牌可出不能过======");
                return;
            }

        }
        var _Opt = (msg && msg.Opt) ? msg.Opt : 0;
        this.doPassCard(pl, tb, _Opt);

    } else if (isEndGame || isReadyGame) {
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', { uid: pl.uid, onLine: true, mjState: pl.mjState, isTrust: msg.isTrust });
        if (this.checkIsAllReady(tb)) {

            if (this.isDoPiaofen(tb)) {
                this.doPiaofen(tb);
            }
            else if (this.isDoAfterReady(tb)) {
                this.doAfterReady(tb);
            } else {
                tb.runStartGame();
            }
        }
    }
};

GameCodeChunTian.prototype.EndRoom = function (tb, msg) {
    var playInfo = null;
    var self = this;
    if (tb.tData.roundNum > -2) {
        this.checkWinAllFanBei(tb);
        this.checkWinJiaFen(tb);
        tb.calculateTableFinalScore();
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function (p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({ userId: p.uid, lastGameTime: p.info.lastGameTime });
                self.calGrade_paodekuaiTY(tb, p)
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if (!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('winall', 'isNeedFanBei', 'lastOffLineTime', 'shuffled', 'winall2');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeChunTian.prototype.countZhaDanScore = function (tb, pl) {
    var tData = tb.tData;
    var difen = tData.areaSelectMode.difen;

    // 炸弹算分
    tb.AllPlayerRun(function (zhaPl) {
        zhaPl.mjdesc1.push("剩余:" + zhaPl.mjhand.length);
        zhaPl.mjdesc1.push("炸弹:" + zhaPl.zhaDanCount);
        //岳阳跑得快使用下面，以后会同步
        if (zhaPl.mjhand.length != 0) zhaPl.mjdesc0.push("剩余:" + zhaPl.mjhand.length + "张");
        if (zhaPl.zhaDanCount != 0) zhaPl.mjdesc0.push("炸弹:" + zhaPl.zhaDanCount);

        if (zhaPl.zhaDanCount > 0) {
            zhaPl.roomStatistics[1] += zhaPl.zhaDanCount;
            tb.AllPlayerRun(function (payPl) {
                var zhaniaozhadan = 1;// tData.areaSelectMode.ht10zhaniaohanzhadan? 2:1;//扎鸟韩炸弹翻倍
                if (zhaPl.uid == tData.uids[tData.ht10Player] || payPl.uid == tData.uids[tData.ht10Player]) {
                    zhaniaozhadan = tData.areaSelectMode.ht10zhaniaohanzhadan ? 2 : 1;//扎鸟韩炸弹翻倍
                }

                if (payPl != zhaPl) {
                    var tempScore = 10 * zhaPl.zhaDanCount * difen * zhaniaozhadan;

                    payPl.winone -= tempScore;
                    zhaPl.winone += tempScore;

                    if (difen < 1) {
                        payPl.winone = revise(payPl.winone);
                        zhaPl.winone = revise(zhaPl.winone);
                    }
                }
            })
        }

    })

    tb.AllPlayerRun(function (zhaPl) {
        zhaPl.zhaDanScore = zhaPl.winone;
    })
}

/**
 * 算分
 * 1）先出完算胜，两个输家扣分给赢家，输家手里剩的牌每张算1分
 * 2）剩1张不扣分，显示-0
 * 3）剩16张翻倍，-32分
 * @param  {Object} tb 桌子对象
 * @param  {Object} pl 胜利玩家对象
 * @return {null} 
 */
GameCodeChunTian.prototype.countScore = function (tb, pl) {

    var isHadZhaDan = false;
    var tData = tb.tData;
    console.log("countScore=====开始算分=====", tData.zhuang);


    var difen = tData.areaSelectMode.difen;
    var players = tData.uids;
    var naturalSpringWinner = tData.naturalSpringWinner;

    var ziranchun = 1;
    if (tData.areaSelectMode.ziranchun) {
        ziranchun = 2;
    }
    // 自然春
    if (naturalSpringWinner != undefined && naturalSpringWinner != 0) {
        // 自然春动画已经在发牌阶段发送，这里不再重复发送

        // 设置自然春玩家为下一局庄家
        tData.winner = tData.uids.indexOf(naturalSpringWinner);

        // 自然春玩家获胜，其他玩家失败
        players.forEach(function (uid) {
            if (uid !== naturalSpringWinner) {
                tb.players[uid].winone = -difen * 10 * 2 * ziranchun;
            } else {
                tb.players[uid].winone = difen * 10 * 2 * (players.length - 1) * ziranchun;
            }
        });
    }
    // 如果是自然春，跳过其他游戏逻辑，直接到统计部分
    else {
        //反春结算
        // 反春条件：
        // 1. 没有报春玩家（无人报春或庄家不同意报春）
        // 2. 庄家出牌1次后，被闲家接上
        // 3. 庄家后续所有牌都要不起，闲家春天

        const isNoBaoChun = !tData.isBaoChun || !tData.baoChunPlayer;
        const isZhuangPutOnce = this.firstPlayerPutCount == 1;

        // 检查是否有闲家春天（手牌全部出完）
        let fanchunWinner = null;
        tb.AllPlayerRun(p => {
            // 跳过庄家，检查闲家是否春天
            if (p.uid !== tData.uids[tData.firstIndex] && p.mjhand.length === 0) {
                fanchunWinner = p.uid;
            }
        });

        // 反春触发条件：无报春 + 庄家出牌1次 + 有闲家春天
        if (isNoBaoChun && isZhuangPutOnce && fanchunWinner) {
            // 无论是否勾选反春规则，都显示反春动画
            tb.NotifyAll("fanchun", { uid: fanchunWinner });

            // 只有勾选了反春规则才进行分数计算
            if (tData.areaSelectMode.fanchun) {
                const difen = tData.areaSelectMode.difen || 1;
                const fanchunScore = 20 * difen; // 反春分数

                tb.AllPlayerRun(p => {
                    // 庄家(先手玩家)扣分
                    if (p.uid === tData.uids[tData.firstIndex]) {
                        p.winone -= fanchunScore * (tData.maxPlayer - 1);
                        p.mjdesc.push("被反春");
                    }
                    // 春天的闲家多得反春分
                    else if (p.uid === fanchunWinner) {
                        p.winone += fanchunScore;
                        p.mjdesc.push("反春");
                    }
                    // 其他闲家正常得分
                });

                logger.debug(`反春触发：庄家=${tData.uids[tData.firstIndex]}，反春玩家=${fanchunWinner}`);
                return;
            }
        }

    // 报春结算
    if (tData.isBaoChun && tData.baoChunPlayer) {
        var fanbei = 1;
        if (tData.areaSelectMode.shachun) {
            fanbei = 2;
        }
        if (tData.baoChunSuccess && tData.baoChunWinner === tData.baoChunPlayer) {
            // 报春玩家胜 - 发送春天动画
            tb.NotifyAll("chuntian", { uid: tData.baoChunPlayer });

            // 设置报春成功的玩家为下一局庄家
            tData.winner = tData.uids.indexOf(tData.baoChunPlayer);

            players.forEach(function (uid) {
                if (uid !== tData.baoChunPlayer) {
                    tb.players[uid].winone = -difen * 10 * 2 * fanbei;
                } else {
                    tb.players[uid].winone = difen * 10 * 2 * (players.length - 1) * fanbei;
                }
            });
        } else if (tData.baoChunFailed && tData.baoChunWinner && tData.baoChunWinner !== tData.baoChunPlayer) {
            // 报春玩家失败 - 发送杀春动画（杀死报春玩家）
            // 无论是否勾选杀春规则，都显示杀春动画
            tb.NotifyAll("shachun", { uid: tData.baoChunWinner });

            // 如果被杀的是庄家，则杀死庄家的玩家成为下一局庄家
            if (tData.baoChunPlayer === tData.uids[tData.zhuang]) {
                tData.winner = tData.uids.indexOf(tData.baoChunWinner);
                logger.debug("庄家被杀春，杀死庄家的玩家成为下一局庄家:", tData.baoChunWinner, "索引:", tData.winner);
            }

            players.forEach(function (uid) {
                if (uid === tData.baoChunWinner) {
                    tb.players[uid].winone = difen * 10 * 2 * (players.length - 1) * fanbei;
                } else if (uid === tData.baoChunPlayer) {
                    tb.players[uid].winone = -difen * 10 * 2 * (players.length - 1) * fanbei;
                } else {
                    tb.players[uid].winone = 0;
                }
            });
        }
        return;
    }


    // 赢牌的人 报单时， 上家出的单牌不是手牌最大的， 需要包分
    var baoFenPlayer = null;
    var winIndex = tb.tData.uids.indexOf(pl.uid);   // 赢牌的人
    var lastPlayer = this.getNextOffPlayer(winIndex, tb, -1); // 赢牌人的上家
    if (lastPlayer.noPutMaxInBaoDan)
        baoFenPlayer = lastPlayer;

    tb.AllPlayerRun(function (p) {
        var niaofanbei = 1;//红桃10扎鸟翻倍（湘乡红桃9扎鸟翻倍）
        var niaojifen = 0;//红桃10扎鸟积分
        var piaofen = 0;//飘分

        //岳阳跑得快使用下面，以后会同步
        if (p.jiazhuNum && p.jiazhuNum != 0) p.mjdesc0.push("飘分:" + p.jiazhuNum + "分");

        // 红桃10扎鸟，则拿到红桃10的玩家，最后输赢分x2，牌型称为【扎鸟】
        if (p.mjhand.length > 1) {
            if (pl.uid == tData.uids[tData.ht10Player]) {
                // 红桃10翻倍
                if (tData.areaSelectMode.hongTao10Niao || tData.areaSelectMode.zhaniao == 2)
                    niaofanbei = 2;
                // 红桃10加5分
                if (tData.areaSelectMode.hongTao10JiaFen || tData.areaSelectMode.zhaniao == 3)
                    niaojifen = 5;
                else if (tData.areaSelectMode.zhaniao == 4)
                    niaojifen = 10;
            } else if (pl.uid == tData.uids[tData.ht9Player]) {
                // 红桃9翻倍
                niaofanbei = 2;
            }
        }

        // 输家扣分
        if (p != pl) {
            var cardNum = p.mjhand.length;
            var maxCardNum = tData.handCount;

            var handCardNum = 0 == tData.areaSelectMode['cardNumIndex'] ? 16 : 15;
            if (cardNum == handCardNum - 1) {  // 小关：只出了一张，手牌剩余15张，计24张

                p.mjdesc3.push("小关");

            } else if (cardNum == handCardNum) { // 大关：一张未出，手牌剩余16张，计32张。

                p.mjdesc3.push("大关");
            }

            // 一张没打(闭门)翻倍， 手上剩一张不用给分
            var cutScore = 0;
            var fanbei = 1;
            if (cardNum == maxCardNum) {
                fanbei = 2;

                // 发送春天动画（被关春天）
                tb.NotifyAll("chuntian", { uid: pl.uid });

                // 如果防强关 且 赢牌人是p的下家 春天不翻倍
                if (p.isFangQiangGuan && lastPlayer == p) {
                    fanbei = 1;
                    p.mjdesc.push("间隙春天");
                }

            } else if (cardNum == 1 && !tb.createParams.fieldId) {
                fanbei = 0;
            }
            var jiakou = 0;
            if (tData.areaSelectMode.jiakoufen) {
                var outnum = maxCardNum - cardNum;
                if (outnum == 1)
                    jiakou = 10;
                else if (outnum >= 2 && outnum <= 3)
                    jiakou = 5
            }
            if (jiakou != 0) {
                p.mjdesc0.push("加扣:" + jiakou + "分");
            }

            cutScore = (cardNum + jiakou) * fanbei;

            if (p.mjhand.length > 1) {
                if (p.uid == tData.uids[tData.ht10Player]) {
                    // 红桃10翻倍
                    if (tData.areaSelectMode.hongTao10Niao || tData.areaSelectMode.zhaniao == 2)
                        niaofanbei = 2;
                    // 红桃10加5分
                    if (tData.areaSelectMode.hongTao10JiaFen || tData.areaSelectMode.zhaniao == 3)
                        niaojifen = 5;
                    else if (tData.areaSelectMode.zhaniao == 4)
                        niaojifen = 10;
                } else if (p.uid == tData.uids[tData.ht9Player]) {
                    // 红桃9翻倍
                    niaofanbei = 2;
                }
            }

            //飘分计算
            if (tData.areaSelectMode.piaofen && tData.areaSelectMode.piaofen != 0 && p.mjhand.length > 1) {
                piaofen = (p.jiazhuNum + pl.jiazhuNum);// * difen;
            }

            var tmpScore = (piaofen + cutScore * niaofanbei + niaojifen) * difen;
            if (baoFenPlayer) {
                baoFenPlayer.winone = revise(baoFenPlayer.winone - tmpScore);
            } else {
                p.winone = revise(p.winone - tmpScore);
            }
            pl.winone = revise(pl.winone + tmpScore);
        }
    }.bind(this));



    tb.AllPlayerRun(function (p) {
        if (p.roomStatistics[2] < p.winone) p.roomStatistics[2] = p.winone;    // 大结算 单局最高得分
        if (p.mjhand.length == tData.handCount) p.roomStatistics[3]++;        // 大结算 被关春天
        if (p.uid == tData.uids[tData.ht10Player] || p.uid == tData.uids[tData.ht9Player]) {
            p.roomStatistics[4]++;      // 大结算 扎鸟
            p.mjdesc.push("扎鸟");
        }
        if (p.winone < 0) {
            p.roomStatistics[5]++;  // 失败次数
        }
    })
    } // 闭合自然春的else语句
}


GameCodeChunTian.prototype.EndGame = function (tb, pl, byEndRoom) {
    logger.debug("========GameCodeChunTian.prototype.endGame======== pl:", pl, "tData.winner:", tb.tData.winner);
    // 修正：如果pl为undefined，尝试用tData.winner获取玩家对象
    if (!pl && typeof tb.tData.winner !== 'undefined') {
        pl = tb.getPlayer(tb.tData.uids[tb.tData.winner]);
        logger.debug("========EndGame修正: 通过tData.winner获取pl:", pl);
    }
    var tData = tb.tData;
    tData.tState = TableState.roundFinish;



    if (byEndRoom) {
        // 耒阳结算算炸弹分
        if (utils.isContains(['leiyang', 'yueyang', 'yueyang-test', 'leiyang-test', 'dev-c'], env)) {
            this.countZhaDanScore(tb, pl);
        }
        else {
            tb.AllPlayerRun(function (zhaPl) {
                zhaPl.mjdesc1.push("剩余:" + zhaPl.mjhand.length);
                zhaPl.mjdesc1.push("炸弹:" + zhaPl.zhaDanCount);
                //岳阳跑得快使用下面，以后会同步
                if (zhaPl.mjhand.length != 0) zhaPl.mjdesc0.push("剩余:" + zhaPl.mjhand.length + "张");
                if (zhaPl.zhaDanCount != 0) zhaPl.mjdesc0.push("炸弹:" + zhaPl.zhaDanCount);
            });
        }
        tb.showDissmissDesc();
    }
    else if (pl) {
        // this.countZhaDanScore(tb, pl);
        this.countScore(tb, pl);
    }
    this.checkMaxScore(tb, pl);
    tb.goldfieldScore();
    var difen = tData.areaSelectMode.difen;
    tb.AllPlayerRun(function (p) {
        p.mjdesc2.push("炸弹分:" + p.zhaDanScore);
        p.mjdesc2.push("总分:" + p.winone);
        //岳阳跑得快使用下面，以后会同步
        if (p.zhaDanScore != 0) p.mjdesc0.push("炸弹分:" + p.zhaDanScore + "分");
        p.mjdescScore.push(p.winone);
        // p.winall = revise(p.winall + p.winone);
        p.mjState = TableState.roundFinish;
        p.handCount = tData.handCount || 16;
    });


    //低分解散不为负数
    tb.calculateRoundFinalScore();

    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }
    //联盟个人排名平衡
    var playersScoreData = {};
    if (tb.leagueRankBalance) {
        tb.AllPlayerRun(function (p) {
            p.winone = revise(p.winone);
            playersScoreData[p.uid] = p.winone / difen;
        });
        tb.updateGameTypeScoreRank(playersScoreData);
    }
    //降低免扣功能数据统计
    tb.statisticsAnalyse();

    tb.perRoundEndDo();
    tData.roundNum--;
    tb.checkExtraTime(byEndRoom);
    if (byEndRoom || tData.roundNum == 0) {
        this.checkWinAllFanBei(tb);
        this.checkWinJiaFen(tb);
        tb.calculateTableFinalScore();
    }

    tb.AllPlayerRun((p) => {
        //解散或者整个局结束，不托管
        if (p.trust) {
            p.trustWholeTimes -= 1;
        }
        if (p.trustWholeTimes > 0 && !byEndRoom && tData.roundNum > 0) {
            tb.trustWhole(p);
        } else {
            tData.roundNum = 0;
        }
    });
    tData.otherCards = tb.pdkOtherCards;
    var roundEnd = {
        players: tb.collectPlayer('mjdescScore', 'mjdesc0', 'mjdesc', 'mjdesc1', 'mjdesc2', 'mjdesc3', 'mjhand', 'handScore', 'selectedCards', 'winone', 'winall', 'info', 'jiazhuNum', 'roomStatistics', "handCount", "zhaDanCount", "ht10Player", 'lastOffLineTime', 'mjhandRecord', 'isNeedFanBei', 'winone2', 'winall2'),
        tData: tData,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };
    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);//一局结束
    logger.debug('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');

    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb);//结束
    if (playInfo) roundEnd.playInfo = playInfo;

    this.checkCardsDataBase(tb)//是否存储牌到数据库

    logger.debug("EndGame============服务器告诉客户端一局完事了============");
    tData.lastPutPlayer = -1;
    tb.NotifyAll("roundEnd", roundEnd);
    tData.lastPutCard = null;
    tb.AllPlayerRun(function (p) {
        p.mjhand = [];
    });

    if (tb.createParams.fieldId)
        tData.allPutCards = [];
};

GameCodeChunTian.prototype.checkRoundEndReconnect = function (tb, pl) {
    if (pl.mjState != TableState.roundFinish) {
        return;
    }
    var tData = tb.tData;
    var roundEnd = {
        players: tb.collectPlayer('mjdescScore', 'mjdesc0', 'mjdesc', 'mjdesc1', 'mjdesc2', 'mjdesc3', 'mjhand', 'handScore', 'selectedCards', 'winone', 'winall', 'info', 'jiazhuNum', 'roomStatistics', "handCount", "zhaDanCount", "ht10Player", 'lastOffLineTime', 'mjhandRecord', 'isNeedFanBei', 'winone2', 'winall2'),
        tData: tData,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: false,
    };
    pl.notify("roundEnd", roundEnd);
};

GameCodeChunTian.prototype.DestroyTable = function (tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
};

// 胜利的人坐庄(第一局庄家为-1) 发牌后在 doAfterHands 计算出牌的人
GameCodeChunTian.prototype.countZhuang = function (tData, tb) {
    if (tData.winner >= 0) {
        tData.zhuang = tData.winner;
        tData.curPlayer = tData.zhuang;
        tData.firstIndex = tData.curPlayer;
    } else {
        // 第一局随机选择庄家
        if (tData.zhuang === undefined || tData.zhuang < 0) {
            tData.zhuang = Math.floor(Math.random() * tData.maxPlayer);
        }
    }

    logger.debug("tData.curPlayertData.curPlayertData.curPlayertData.curPlayer=====0=====", tData.curPlayer);
};


GameCodeChunTian.prototype.doClubHuanPai = function (scrHands, desHands, caiType, tb) {
    //牌型示例(用来确定张数)
    var cardsTmp = {
        [this.majiang.CARDTPYE.liandui]: [1, 1, 1, 1, 1, 1, 1, 1],
        [this.majiang.CARDTPYE.shunzi]: [1, 1, 1, 1, 1, 1, 1],
        [this.majiang.CARDTPYE.sanshun]: [1, 1, 1, 1, 1, 1, 1, 1, 1],
        [this.majiang.CARDTPYE.sizha]: [1, 1, 1, 1],
        [this.majiang.CARDTPYE.sangeA]: [1, 1, 1],
        [this.majiang.CARDTPYE.sange3]: [1, 1, 1],
    };
    var caiCntoff = //拥有的张数减去这个数，就是要拆掉的张数
    {
        [this.majiang.CARDTPYE.liandui]: 1,
        [this.majiang.CARDTPYE.shunzi]: 0,
        [this.majiang.CARDTPYE.sanshun]: 2,
        [this.majiang.CARDTPYE.sizha]: 3,
        [this.majiang.CARDTPYE.sangeA]: 2,
        [this.majiang.CARDTPYE.sange3]: 2,
    };

    if (!cardsTmp[caiType]) return false;//不在拆牌范围类

    logger.debug("===============huaipai", scrHands, desHands, caiType)

    var chaiCards = [];//scrHands中拆掉的牌
    var chouCards = [];//desHands抽出的牌

    //找出要拆的牌
    var tmpScr = scrHands.slice();
    var Typecards = [];//当前需要被拆的牌型
    var caiptAry = [];//拆掉的牌值数组
    while (true) {
        Typecards = this.majiang.findCardByType(tmpScr, 0, caiType, cardsTmp[caiType], [], tb.tData.areaSelectMode);
        logger.debug("===============1111", Typecards)
        if (!Typecards || Typecards.length == 0) break;

        var crAry = [];
        var minCnt = 1000;
        var caipt = -1;
        //
        for (var i = 0; i < Typecards[0].length; i++) {
            var pt = this.majiang.calPoint(Typecards[0][i]);
            if (crAry.indexOf(pt) >= 0) continue;
            crAry.push(pt);
            var c = 0
            for (var j = 0; j < tmpScr.length; j++) {
                if (this.majiang.calPoint(tmpScr[j]) == pt) c++;
            }
            if (c < minCnt) {
                minCnt = c;
                caipt = pt;
            }
        }

        caiptAry.push(caipt);//缓存被拆掉的牌值

        for (var i = 0; i < minCnt - caiCntoff[caiType]; i++) {
            for (var j = 0; j < tmpScr.length; j++) {
                if (this.majiang.calPoint(tmpScr[j]) == caipt) {
                    chaiCards.push(tmpScr[j]);
                    tmpScr.splice(j, 1);
                    break;
                }
            }
        }
        if (caiType == this.majiang.CARDTPYE.sizha ||
            caiType == this.majiang.CARDTPYE.sangeA ||
            caiType == this.majiang.CARDTPYE.sange3)
            break;//炸弹只拆掉一个
    }
    logger.debug("===============", caiptAry);
    //判断满足换牌条件吗
    if (chaiCards.length == 0) return false;
    logger.debug("===============2222222222222")
    //找出要换掉的牌;
    var chouptAry = [];//抽出的牌值
    desHands.sort(this.majiang.cardValueCmp.bind(this.majiang));
    for (var i = 0; i < desHands.length; i++) {
        var pt = this.majiang.calPoint(desHands[i]);
        if (caiptAry.indexOf(pt) >= 0 || chouptAry.indexOf(pt) >= 0) continue;
        chouCards.push(desHands[i]);
        chouptAry.push(pt);
        if (chouCards.length == chaiCards.length) break;
    }

    //判断满足换牌条件吗
    if (chouCards.length != chaiCards.length) return false;
    logger.debug("===============333333333333333")
    //删除scrHands中拆掉的牌,desHands加入scrHands中拆掉的牌
    for (var i = 0; i < chaiCards.length; i++) {
        for (var j = 0; j < scrHands.length; j++) {
            if (chaiCards[i] == scrHands[j]) {
                scrHands.splice(j, 1);
                break;
            }
        }
        desHands.push(chaiCards[i]);
    }

    //scrHands加入desHands中抽出的牌,删除desHands中抽出的牌
    for (var i = 0; i < chouCards.length; i++) {
        for (var j = 0; j < desHands.length; j++) {
            if (chouCards[i] == desHands[j]) {
                desHands.splice(j, 1);
                break;
            }
        }
        scrHands.push(chouCards[i]);
    }

    return true;
}

// 获取除去打出的牌后，剩余的牌
GameCodeChunTian.prototype.getLeftCards = function (tb) {
    var tData = tb.tData;
    var leftCards = tb.cards.slice();

    if (!tData.allPutCards[0])
        return leftCards.concat(tData.otherCards);

    var allPuts = tData.allPutCards.slice();
    for (var i = 0; i < leftCards.length;) {
        var index = allPuts.indexOf(leftCards[i]);
        if (index >= 0) {
            leftCards.splice(i, 1);
            allPuts.splice(index, 1);

            if (!allPuts[0])
                break;
        } else
            i++;
    }

    return leftCards.concat(tData.otherCards);
};

// 使用记牌器后的处理
GameCodeChunTian.prototype.useJipaiqi = function (pl, tb) {
    var tData = tb.tData;

    // 跑得快金币场相关
    if (tData.jipaiqi && utils.isContains(tData.jipaiqi, pl.uid)) {
        pl.notify("initJiPaiQi", { jipaiqi: tData.jipaiqi, leftCards: this.getLeftCards(tb) });
    }
};

GameCodeChunTian.prototype.checkWinAllFanBei = function (tb) {

    if (tb.tData.areaSelectMode.fanBei == 1) {
        var max = -1;
        tb.AllPlayerRun(function (p) {
            max = p.winall > max ? p.winall : max;
        });
        if (max < tb.tData.areaSelectMode.fanBeiScore) {
            tb.AllPlayerRun(function (p) {
                if (!p.isNeedFanBei) {
                    p.winall *= 2;
                    //p.winall += 10;         // 翻倍改为加10分
                    p.isNeedFanBei = true;
                }
            });
        }
    }


};

GameCodeChunTian.prototype.checkWinJiaFen = function (tb) {

    if (tb.tData.areaSelectMode.jiaFen > 0 &&
        2 == tb.tData.maxPlayer) {
        var max = 0;
        tb.AllPlayerRun(function (p) {
            max = p.winall > max ? p.winall : max;
        });
        if (max <= 0) return;

        tb.AllPlayerRun(function (p) {
            if (p.isNeedJiaFen) return;
            if (p.winall > 0) p.winall += tb.tData.areaSelectMode.jiaFen;
            else p.winall -= tb.tData.areaSelectMode.jiaFen;
            p.isNeedJiaFen = true;
        });
    }
};


//在每局结束时，检查是否存入牌型库
GameCodeChunTian.prototype.checkCardsDataBase = function (tb) {
    var tData = tb.tData;
    if (tData.firstIndex < 0 || tData.firstIndex >= tData.maxPlayer || tb.createParams.fieldId || tb.tData.areaSelectMode.havezhadan == false) return;
    if (tData.UseCardsInDatabaseData)//如果是牌型库读取的局，就不走存储逻辑
        return;
    //5位二进制，
    //第一二位，分数     
    //第三位，是否先手 ----0：是，1：否，
    //第四位，人数     ----0：3人，1：2人，
    //第五位，牌张数   ----0：15张，1：16张，
    var typeData =
    {
        [0b00001]: 1,//  * 1：通用跑得快15张3人，先手20+
        [0b00101]: 2,//  * 2：通用跑得快15张3人，非先手15+  ============
        [0b01001]: 3,//  * 3：通用跑得快15张2人，先手10+
        [0b01101]: 4,//  * 4：通用跑得快15张2人，非先手10+
        [0b10001]: 5,//  * 5：通用跑得快16张3人，先手20+
        [0b10101]: 6,//  * 6：通用跑得快16张3人，非先手15+  ============
        [0b11001]: 7,//  * 7：通用跑得快16张2人，先手10+    ============
        [0b11101]: 8 //  * 8：通用跑得快16张2人，非先手10+  ============
    }

    var winMax = 0;//最大赢家分
    var winMaxPlyId = 0;//最大赢家ID
    tb.AllPlayerRun(function (p) {
        if (p.winone > winMax) {
            winMax = p.winone;
            winMaxPlyId = p.uid;
        }
    });

    //var isFirst = (tData.uids[( tData.zhuang + tData.maxPlayer) % tData.maxPlayer] == winMaxPlyId);//赢家是否先手
    var isFirst = (tData.uids[tData.firstIndex] == winMaxPlyId);//赢家是否先手
    //type索引
    var typeIndex = 0b00000;
    //张数
    typeIndex = typeIndex | ((0 == tData.areaSelectMode['cardNumIndex']) ? 0b10000 : 0b00000);
    //人数
    typeIndex = typeIndex | ((2 == tb.tData.maxPlayer) ? 0b01000 : 0b00000);
    //是否先手
    typeIndex = typeIndex | (isFirst ? 0b00000 : 0b00100);
    //分数
    if (2 == tb.tData.maxPlayer)
        typeIndex = typeIndex | ((winMax >= 10) ? 0b00001 : 0b00000);
    else if (3 == tb.tData.maxPlayer) {
        if (isFirst)
            typeIndex = typeIndex | ((winMax >= 20) ? 0b00001 : 0b00000);
        else
            typeIndex = typeIndex | ((winMax >= 15) ? 0b00001 : 0b00000);
    }

    if (!typeData[typeIndex]) {
        logger.debug("牌型数据存储类型不存在===============", typeIndex);
        return;
    }

    //还原发的牌
    var cards = [];
    var winIndex = -1;
    for (var i = 0; i < tData.maxPlayer; i++) {
        var pl = tb.players[tData.uids[(i + tData.firstIndex + tData.maxPlayer) % tData.maxPlayer]];
        cards = cards.concat(pl.mjhandRecord);
        if (pl.uid == winMaxPlyId) winIndex = i;
    }
    if (winIndex < 0) return;
    if (tb.tData.otherCards && tb.tData.otherCards.length > 0)
        cards = cards.concat(tb.tData.otherCards);

    logger.debug("牌型数据存储类型正确===============", tData.areaSelectMode['cardNumIndex'], tb.tData.maxPlayer, isFirst, winMax, typeIndex, winIndex);

}
// 在每局结束时，检查是否存入牌型库
GameCodeChunTian.prototype.needUseCardsInDatabase = function (tb) {

    var tData = tb.tData;

    if (tData.roundNum > tData.roundAll - 4 || //第五局才开始
        tb.createParams.fieldId ||//金币场不走
        tb.tData.areaSelectMode.isPreRoundFirstRule || //每局先手规则不走
        !tb.createParams.clubId) //俱乐部房间才走
    {
        return { need: false };
    }

    var playerScores = [];
    tb.AllPlayerRun(function (p) {
        playerScores.push(p.winall);
    });
    playerScores.sort(function (score_1, score_2) {
        return score_2 - score_1;
    });

    var winMax = playerScores[0];  //最大赢家分
    var secondScore = playerScores[1];  // 第二名分数
    var winMaxPlyId = null;  // 大赢家id
    var secondPlyId = null;  // 第二名id

    tb.AllPlayerRun(function (p) {
        if (!winMaxPlyId && p.winall == winMax) {
            winMaxPlyId = p.uid;
        } else if (!secondPlyId && p.winall == secondScore) {
            secondPlyId = p.uid;
        }
    });

    // 该赢分需除以底分以消除底分影响
    winMax /= tData.areaSelectMode.difen;

    var playerIndex = -1;
    if (winMax < 25) {
        // 目前赢分最多（以下统称大赢家）的玩家，赢分<25
        // 选择该玩家，配发牌
        playerIndex = tData.uids.indexOf(winMaxPlyId);
    } else if (winMax > 89) {
        // 目前大赢家赢分>89
        // 选择其余玩家中成绩最好的一个，配发牌
        playerIndex = tData.uids.indexOf(secondPlyId);
    }

    if (playerIndex > -1) {
        //type索引
        var typeIndex = 0b00000;
        //张数
        typeIndex = typeIndex | ((0 == tData.areaSelectMode['cardNumIndex']) ? 0b10000 : 0b00000);
        //人数
        typeIndex = typeIndex | ((2 == tb.tData.maxPlayer) ? 0b01000 : 0b00000);
        //是否先手
        typeIndex = typeIndex | (tData.zhuang == playerIndex ? 0b00000 : 0b00100);
        //分数
        // if (2 == tb.tData.maxPlayer)
        //    typeIndex = typeIndex | ( (winMax >= 10) ? 0b00001 : 0b00000);
        // else if (3 == tb.tData.maxPlayer)
        // {
        //     if (tData.zhuang == playerIndex)
        //         typeIndex = typeIndex | ( (winMax >= 20) ? 0b00001 : 0b00000);
        //     else
        //         typeIndex = typeIndex | ( (winMax >= 15) ? 0b00001 : 0b00000);
        // }
        typeIndex = typeIndex | 0b00001;
        var typeData =
        {
            [0b00001]: 1,//  * 1：通用跑得快15张3人，先手20+
            [0b00101]: 2,//  * 2：通用跑得快15张3人，非先手15+  ============
            [0b01001]: 3,//  * 3：通用跑得快15张2人，先手10+
            [0b01101]: 4,//  * 4：通用跑得快15张2人，非先手10+
            [0b10001]: 5,//  * 5：通用跑得快16张3人，先手20+
            [0b10101]: 6,//  * 6：通用跑得快16张3人，非先手15+  ============
            [0b11001]: 7,//  * 7：通用跑得快16张2人，先手10+    ============
            [0b11101]: 8 //  * 8：通用跑得快16张2人，非先手10+  ============
        }

        typeIndex = typeData[typeIndex];

        if (typeIndex) {
            playerIndex = (playerIndex - tData.zhuang + tData.maxPlayer) % tData.maxPlayer;

            logger.debug("需要从数据库中读取配牌，参数是： ", typeIndex, playerIndex);

            return { need: true, typeIndex: typeIndex, playerIndex: playerIndex };
        }
    }

    logger.debug("不需要从数据库中读取配牌");

    return { need: false };
}

GameCodeChunTian.prototype.UseCardsInDatabase = function (tb, typeIndex, playerIndex) {
    var tData = tb.tData;

    //读取牌型库
    var cardsNew = tb.cardTypeCall(typeIndex, playerIndex);
    if (!cardsNew) return;
    var cardCount = (0 == tData.areaSelectMode['cardNumIndex']) ? 10 : 10;
    logger.debug("从数据库中读取配牌===", JSON.stringify(cardsNew));
    if (cardCount * 3 != cardsNew.length) {
        return;
    }

    //替换要发的牌

    for (var i = 0; i < tData.maxPlayer; i++) {
        var pl = tb.players[tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]];
        pl.mjhand = cardsNew.splice(0, cardCount);
        if (tData.maxPlayer == 2 && i == tData.maxPlayer - 1) {//othercards
            tData.otherCards = cardsNew.splice(0, cardCount);
        }
    }

    //记录---房间号 第几局 时间  15张/16张 3人玩/2人玩 增强对象ID  对象是先手/后手    对象本局结算分
    tData.UseCardsInDatabaseData = {};
    tData.UseCardsInDatabaseData.roomid = tb.tableid;
    tData.UseCardsInDatabaseData.roundnum = tData.roundAll - tData.roundNum + 1;
    tData.UseCardsInDatabaseData.time = Date.now();
    tData.UseCardsInDatabaseData.cardnum = tData.areaSelectMode['cardNumIndex'];
    tData.UseCardsInDatabaseData.playnum = tData.maxPlayer;
    var powerID = tData.uids[(playerIndex + tData.zhuang) % tData.maxPlayer]
    tData.UseCardsInDatabaseData.powerplayer = powerID;
    tData.UseCardsInDatabaseData.isfirst = (tData.uids[tData.zhuang] == powerID);
    tData.UseCardsInDatabaseData.powerplayerscore = 0;

    logger.debug("==============", JSON.stringify(tData.UseCardsInDatabaseData));
}

GameCodeChunTian.prototype.checkMaxScore = function (tb, winPl) {
    var tData = tb.tData;
    var difen = tData.areaSelectMode.difen;
    //算出封顶分数
    var MaxScore = 999999;
    if (tData.areaSelectMode.fengDing == 1)
        MaxScore = (0 == tData.areaSelectMode['cardNumIndex']) ? 32 : 30;
    else if (tData.areaSelectMode.fengDing == 2)
        MaxScore = (0 == tData.areaSelectMode['cardNumIndex']) ? 64 : 60;
    else if (tData.areaSelectMode.fengDing == 3)
        MaxScore = (0 == tData.areaSelectMode['cardNumIndex']) ? 128 : 120;
    if (MaxScore == 999999) return;



    //算出输家分数和总输分
    var allLoseScore = 0;
    tb.AllPlayerRun(function (p) {
        p.winone = p.winone / difen;
        if (p.winone >= 0) return;
        if (Math.abs(p.winone) > MaxScore) p.winone = MaxScore * -1;
        allLoseScore += p.winone * -1;
    });

    //玩家优先级
    var plyAry = [];
    if (winPl) plyAry.push(winPl);
    tb.AllPlayerRun(function (p) {
        if (winPl && p.uid == winPl.uid) return;
        plyAry.push(p);
    });

    //正分玩家分数
    for (var i = 0; i < plyAry.length; i++) {
        var p = plyAry[i];
        if (p.winone < 0) continue;
        p.winone = p.winone > allLoseScore ? allLoseScore : p.winone;
        allLoseScore -= p.winone;
    }

    //乘以底分
    tb.AllPlayerRun(function (p) {
        p.winone = revise(p.winone * difen);
    });
}

//小结算洗牌
GameCodeChunTian.prototype.mjShuffle = function (pl, msg, session, next, tb) {
    tb.NotifyAll("MJShuffle", { uid: pl.uid });
    cloneDataAndPush(tb.mjlog, "MJShuffle", { uid: pl.uid });
};

/**************************************************************/
// 整场托管相关
GameCodeChunTian.prototype.checkTrustWholeStatus = function (pl) {
    return pl.mjState == TableState.roundFinish || pl.mjState == TableState.waitJiazhu || pl.mjState == TableState.afterReady;
};

GameCodeChunTian.prototype.trustWholeAction = function (pl, tb) {
    if (pl.mjState == TableState.roundFinish) {
        tb.PKPass(pl, { isTrust: true, cmd: "PKPass" }, null, function () { });
    } else if (pl.mjState == TableState.waitJiazhu) {
        tb.MJJiazhu(pl, { isTrust: true, jiazhuNum: 0 }, null, function () { });
    } else if (pl.mjState == TableState.afterReady) {
        tb.cancelTrustWholeAction(pl);
        tb.updateTrustWholeStatus(pl, true);

        var tData = tb.tData;
        if (pl.uid != tData.uids[tData.curPlayer])
            return;

        tb.c2s_playerShuffleFinish(pl, { cmd: "c2s_playerShuffleFinish", dealyTime: 800 }, null, function () { });
    }
};

GameCodeChunTian.prototype.trustActionDelay = function (tb) {
    return (this.newRoundBegan && tb.tData.areaSelectMode.isTrustWhole) ? 1500 : 700;
}

GameCodeChunTian.prototype.trustWholeDelay = function (tb) {
    if (tb.tData.tState == TableState.roundFinish)
        return 4;
    else
        return 1;
}

// 取消托管接口
GameCodeChunTian.prototype.cancelTrust = function (pl, msg, session, next, tb) {
    if (!pl.trust) {
        return;
    }

    pl.clearTrustTimer();
    tb.NotifyAll('cancelTrust', { uid: pl.uid });
    cloneDataAndPush(tb.mjlog, 'cancelTrust', { uid: pl.uid });
    pl.trust = false;

    // 重置整场托管次数
    pl.trustWholeTimes = [1, 2, 100][tb.tData.areaSelectMode.trustWay || 0];

    // if (!msg || !msg.donotCallDoTrust)
    //     tb.doTrust(pl);

    if (!this.checkTrustWholeStatus(pl, tb)) {
        tb.doTrust(pl);
    }
};

// ========== 春天扑克玩法核心逻辑 ========== //

// 1. 发牌逻辑（54张，2/3/4人，底牌不发完）
GameCodeChunTian.prototype.dealChunTianCards = function (tb) {
    const tData = tb.tData;
    const playerCount = tData.maxPlayer;
    let deck = [];
    for (let i = 1; i <= 52; i++) deck.push(i); // 1~52
    deck.push(53); // 小王
    deck.push(54); // 大王
    // 洗牌
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    let handCount = playerCount === 3 ? 17 : 13;
    let hands = [];
    for (let i = 0; i < playerCount; i++) {
        hands.push(deck.splice(0, handCount));
    }
    tData.otherCards = deck; // 剩余为底牌
    tb.AllPlayerRun((p, idx) => {
        p.mjhand = hands[idx];
    });
};



// 4. 结算与分摊
GameCodeChunTian.prototype.countScoreChunTian = function (tb, winnerIdx, isChunTian) {
    const tData = tb.tData;
    const difen = tData.areaSelectMode.difen || 1;
    const playerCount = tData.maxPlayer;
    tb.AllPlayerRun(p => { p.winone = 0; });
    if (isChunTian) {
        // 春天：庄家赢4倍基础分，三家平均分摊
        const chunTianScore = 4 * difen;
        tb.AllPlayerRun((p, idx) => {
            if (idx === winnerIdx) {
                p.winone += chunTianScore;
            } else {
                p.winone -= chunTianScore / (playerCount - 1);
            }
        });
    } else {
        // 正常结算（上游3倍，二游1倍，三游-1倍，下游-4倍等，按你的规则补充）
        // ...此处补充正常结算逻辑...
    }
};

// 5. 炸弹奖励
GameCodeChunTian.prototype.addZhaDanScore = function (tb, fromIdx, toIdx) {
    const tData = tb.tData;
    const difen = tData.areaSelectMode.difen || 1;
    const score = 4 * difen;
    tb.players[fromIdx].winone -= score;
    tb.players[toIdx].winone += score;
};

// 6. 完全移除原跑得快相关流程，只保留春天扑克
// ... existing code ...
// ====== 报春超时处理 ======
GameCodeChunTian.prototype.handleBaoChunTimeout = function (tb) {
    var tData = tb.tData;

    if (tData.baoChunStatus !== "waiting") {
        return;
    }

    // 清除定时器
    if (tData.baoChunTimer) {
        clearTimeout(tData.baoChunTimer);
        tData.baoChunTimer = null;
    }

    // 将所有未选择的玩家设为不报春
    var zhuangUid = tData.uids[tData.zhuang];
    tb.AllPlayerRun(function (p) {
        if (p.uid !== zhuangUid && tData.baoChunChoices[p.uid] === undefined) {
            tData.baoChunChoices[p.uid] = false;
        }
    });

    // 检查是否有人报春
    var hasBaoChun = false;
    for (var uid in tData.baoChunChoices) {
        if (tData.baoChunChoices[uid]) {
            hasBaoChun = true;
            break;
        }
    }

    if (hasBaoChun) {
        // 有人报春，进入庄家确认阶段
        tData.baoChunStatus = "waitingZhuangConfirm";

        // 设置15秒庄家确认超时
        if (tData.zhuangConfirmTimer) {
            clearTimeout(tData.zhuangConfirmTimer);
        }
        tData.zhuangConfirmTimer = setTimeout(() => {
            this.handleZhuangConfirmTimeout(tb);
        }, 15000);

        tb.NotifyAll("waitZhuangConfirm", {
            baoChunChoices: tData.baoChunChoices,
            timeLimit: 15 // 15秒时间限制
        });
    } else {
        // 没有人报春，直接开始游戏，由庄家先出牌
        tData.baoChunStatus = "finished";
        tData.isBaoChun = false;
        tData.baoChunPlayer = null;
        tData.curPlayer = tData.zhuang; // 设置庄家为当前出牌玩家

        tb.NotifyAll("baoChunResult", { isBaoChun: false });
        this.sendNewCard(tb, tData.curPlayer);
    }
};

// ====== 庄家确认超时处理 ======
GameCodeChunTian.prototype.handleZhuangConfirmTimeout = function (tb) {
    var tData = tb.tData;

    if (tData.baoChunStatus !== "waitingZhuangConfirm") {
        return;
    }

    // 清除定时器
    if (tData.zhuangConfirmTimer) {
        clearTimeout(tData.zhuangConfirmTimer);
        tData.zhuangConfirmTimer = null;
    }

    // 超时自动不同意报春
    this.handleZhuangConfirm(null, { agree: false }, tb);
};

// ====== 报春选择处理 ======
GameCodeChunTian.prototype.baoChun = function (pl, msg, session, next, tb) {
    var tData = tb.tData;

    // 处理庄家确认报春（临时解决方案）
    if (msg.isZhuangConfirm && msg.agree !== undefined) {
        if (tData.baoChunStatus !== "waitingZhuangConfirm") {
            return;
        }

        if (pl.uid !== tData.uids[tData.zhuang]) {
            return;
        }

        // 调用庄家确认逻辑
        this.handleZhuangConfirm(pl, msg, tb);
        return;
    }

    // 原有的报春选择逻辑
    if (tData.baoChunStatus !== "waiting") {
        return;
    }

    // 检查是否是庄家，庄家不能报春
    var zhuangUid = tData.uids[tData.zhuang];
    if (pl.uid === zhuangUid) {
        return;
    }

    tData.baoChunChoices[pl.uid] = !!msg.isBaoChun;

    // 检查是否所有非庄家玩家都已选择
    var allChosen = tData.uids.every(function (uid) {
        // 庄家不参与报春选择，所以跳过庄家
        if (uid === zhuangUid) return true;
        return tData.baoChunChoices.hasOwnProperty(uid);
    });
    if (!allChosen) return;

    // 清除报春选择定时器
    if (tData.baoChunTimer) {
        clearTimeout(tData.baoChunTimer);
        tData.baoChunTimer = null;
    }

    // 检查是否有人选择报春
    var hasPlayerBaoChun = Object.values(tData.baoChunChoices).some(choice => choice);

    if (!hasPlayerBaoChun) {
        // 无人报春，庄家先出牌
        tData.baoChunStatus = "done";
        tData.baoChunPlayer = null;
        tData.isBaoChun = false;
        tData.curPlayer = tData.zhuang; // 设置庄家为当前出牌玩家

        tb.NotifyAll("baoChunResult", { isBaoChun: false });
        this.sendNewCard(tb, tData.curPlayer);
    } else {
        // 有人报春，等待庄家确认
        tData.baoChunStatus = "waitingZhuangConfirm";

        // 设置15秒庄家确认超时
        if (tData.zhuangConfirmTimer) {
            clearTimeout(tData.zhuangConfirmTimer);
        }
        tData.zhuangConfirmTimer = setTimeout(() => {
            this.handleZhuangConfirmTimeout(tb);
        }, 15000);

        var zhuangUid = tData.uids[tData.zhuang];
        tb.NotifyAll("waitZhuangConfirm", {
            zhuangUid: zhuangUid,
            timeLimit: 15 // 15秒时间限制
        });
    }
};

// 庄家确认报春的辅助函数
GameCodeChunTian.prototype.handleZhuangConfirm = function (pl, msg, tb) {
    console.log("=== 庄家确认报春辅助函数开始执行 ===");
    console.log("庄家UID:", pl ? pl.uid : "超时", "确认结果:", msg.agree);

    var tData = tb.tData;
    console.log("当前报春状态:", tData.baoChunStatus);
    console.log("庄家索引:", tData.zhuang, "庄家UID:", tData.uids[tData.zhuang]);

    // 清除庄家确认定时器
    if (tData.zhuangConfirmTimer) {
        clearTimeout(tData.zhuangConfirmTimer);
        tData.zhuangConfirmTimer = null;
    }

    console.log("✅ 庄家确认有效，设置状态为done");
    tData.baoChunStatus = "done";

    if (!msg.agree) {
        // 庄家不同意，庄家成为报春玩家
        tData.baoChunPlayer = tData.uids[tData.zhuang];
        tData.isBaoChun = true;
        tData.curPlayer = tData.zhuang; // 设置庄家为当前出牌玩家

        var player = tb.players[tData.uids[tData.zhuang]];
        var resultData = {
            isBaoChun: true,
            zhuangDisagree: true,
            baoChunPlayer: tData.uids[tData.zhuang],
            baoChunPlayerName: player && player.info ? player.info.nickname : ""
        };

        tb.NotifyAll("baoChunResult", resultData);
        this.sendNewCard(tb, tData.curPlayer);
        return;
    }
    // 庄家同意，从庄家下家开始按顺序寻找第一个报春玩家
    var baoChunPlayer = null;
    var queue = tData.uids;
    var zhuangIdx = tData.zhuang;
    var currentIdx = (zhuangIdx + 1) % queue.length; // 从庄家下家开始

    console.log("报春选择记录:", JSON.stringify(tData.baoChunChoices));
    console.log("玩家队列:", queue);
    console.log("庄家索引:", zhuangIdx, "开始搜索索引:", currentIdx);

    do {
        var uid = queue[currentIdx];
        console.log("检查玩家:", uid, "是否报春:", tData.baoChunChoices[uid]);
        if (tData.baoChunChoices[uid]) {
            baoChunPlayer = uid;
            break;
        }
        currentIdx = (currentIdx + 1) % queue.length;
    } while (currentIdx !== (zhuangIdx + 1) % queue.length);

    // 设置报春结果
    tData.baoChunPlayer = baoChunPlayer;
    tData.isBaoChun = !!baoChunPlayer;

    var resultData = { isBaoChun: !!baoChunPlayer };
    if (baoChunPlayer) {
        // 找到报春玩家的索引
        var baoChunPlayerIdx = tData.uids.indexOf(baoChunPlayer);
        tData.curPlayer = baoChunPlayerIdx;

        var player = tb.players[baoChunPlayer];
        resultData.baoChunPlayer = baoChunPlayer;
        resultData.baoChunPlayerName = player && player.info ? player.info.nickname : "";

        console.log("✅ 报春成功，报春玩家:", baoChunPlayer, "索引:", baoChunPlayerIdx, "昵称:", resultData.baoChunPlayerName);
    } else {
        console.log("❌ 没有找到报春玩家");
    }

    console.log("发送baoChunResult事件:", JSON.stringify(resultData));
    tb.NotifyAll("baoChunResult", resultData);
    this.sendNewCard(tb, tData.curPlayer);
};

module.exports = GameCodeChunTian;
