{"_args": [["after@0.8.2", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "after@0.8.2", "_id": "after@0.8.2", "_inBundle": false, "_integrity": "sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8=", "_location": "/after", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "after@0.8.2", "name": "after", "escapedName": "after", "rawSpec": "0.8.2", "saveSpec": null, "fetchSpec": "0.8.2"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://registry.npm.taobao.org/after/download/after-0.8.2.tgz", "_spec": "0.8.2", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/after/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://raynos.org"}], "description": "after - tiny flow control", "devDependencies": {"mocha": "~1.8.1"}, "homepage": "https://github.com/Raynos/after#readme", "keywords": ["flowcontrol", "after", "flow", "control", "arch"], "license": "MIT", "name": "after", "repository": {"type": "git", "url": "git://github.com/Raynos/after.git"}, "scripts": {"test": "mocha --ui tdd --reporter spec test/*.js"}, "version": "0.8.2"}