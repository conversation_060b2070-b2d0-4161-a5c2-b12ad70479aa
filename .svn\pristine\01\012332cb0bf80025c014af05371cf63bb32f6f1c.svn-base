// 汨罗红字算法
(function() {
    var MaJiangMLHZ = function() {
        this.KING = 91;
        this.handCount = 13;
    };

    var menziList = []; // 所有可能牌型组合
    var duiList = [];
    for (var i = 1; i <= 10; i++) {
        menziList.push([i, i, i], [i + 20, i + 20, i + 20]);
        duiList.push([i, i], [i + 20, i+ 20]);
    }

    for (var i = 1; i <= 8; i++) {
        menziList.push([i, i + 1, i + 2]);
        menziList.push([i + 20, i + 21, i + 22]);
    }

    menziList.push([2, 7, 10], [22, 27, 30]);

    var KING = 91;

    MaJiangMLHZ.prototype.randomCards = function(areaSelectMode) {
        var cards = [
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
            21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
            21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
            21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
        ];

        var kingNum = areaSelectMode.kingNum || 0;
        for (var i = 0; i < kingNum; i++) {
            cards.push(KING);
        }

        shuffleArray(cards);
        return cards;
    };

    // 理牌
    MaJiangMLHZ.prototype.sortCard = function(hand) {
        hand = hand.slice();

        var matrix = [];
        var dict = {};
        for (var i = 0; i < hand.length; i++) {
            dict[hand[i]] = dict[hand[i]] ? dict[hand[i]] + 1 : 1;
        }

        if (dict[KING] > 0) {
            for (var i = 0; i < dict[KING]; i++) {
                hand.splice(hand.indexOf(KING), 1);
            }
        }

        function del(card, num) {
            for (var i = 0; i < num; i++) {
                hand.splice(hand.indexOf(card), 1);
                dict[card]--;
            }
        }

        // 取一句话
        for (var i = 0; i < menziList.length; i++) {
            var row = menziList[i].slice(); // 此处需要拷贝！ 防止外部修改
            if (i < 20) { 
                if (dict[row[0]] >= 3) {
                    matrix.push(row);
                    del(row[0], 3);
                }
            } else {
                if (dict[row[0]] >= 1 && dict[row[1]] >= 1 && dict[row[2]] >= 1) {
                    matrix.push(row);
                    del(row[0], 1);
                    del(row[1], 1);
                    del(row[2], 1);
                    i--;
                }
            }
        }

        // 取对
        for (var k in dict) {
            var cd = Number(k);
            if (cd != KING && dict[k] == 2) {
                matrix.push([cd, cd]);
                del(cd, 2);
            }
        }

        hand.sort();
        var copy = hand.slice();
        // 相连的牌
        function getLink() { // todo 是否加入王
            if (copy.length <= 0) {
                return;
            }

            var card = copy[0];
            if (card == KING) {
                return;
            }

            var linkList = [];
            // 2-7-10
            if (card % 10 == 2) {
                linkList.push(card + 5, card + 8);
            } else if (card % 10 == 7) {
                linkList.push(card + 3);
            }

            // 顺子
            linkList.push(card + 1, card + 2);
            
            var flag = false;
            for (var i = 0; i < linkList.length; i++) {
                var card2 = linkList[i];
                if (dict[card2] > 0) {
                    del(card, 1);
                    del(card2, 1);
                    copy.splice(copy.indexOf(card2), 1);
                    matrix.push([card, card2]);
                    break;
                }
            }
            copy.splice(copy.indexOf(card), 1);
            getLink();
        }
        getLink();
        for (var i = 0; i < hand.length; i++) {
            matrix.push([hand[i]]);
        }

        if (dict[KING] == 4) {
            matrix.push([KING, KING, KING], [KING]);
        } else if (dict[KING] > 0) {
            var row = [];
            for (var i = 0; i < dict[KING]; i++) {
                row.push(KING);
            }
            matrix.push(row);
        }

        for (var i = 0; i < matrix.length; i++) {
            var row = matrix[i];
            row.sort(function(a, b) {
                return a - b;
            })
        }

        matrix.sort(function(a, b) {
            return a[0] - b [0];
        })


        return matrix;
    };

    MaJiangMLHZ.prototype.getCardCount = function(pl, card) {
        var count = 0;
        for (var i = 0; i < pl.mjhand.length; i++) {
            if (pl.mjhand[i] == card) {
                count++;
            }
        }

        return count;
    }

    MaJiangMLHZ.prototype.isRed = function(card) {
        return [2, 7, 10, 22, 27, 30].indexOf(card) >= 0;
    };

    MaJiangMLHZ.prototype.isQiDui = function(pl, card) {
        var hand = pl.mjhand.slice();
        hand.push(card);

        if (hand.length != this.handCount + 1) {
            return false;
        }

        var dict = {};
        dict[KING] = 0;
        for (var i = 0; i < hand.length; i++) {
            dict[hand[i]] = dict[hand[i]] ? dict[hand[i]] + 1 : 1;
        }
        for (var k in dict) {
            if (Number(k) == KING) {
                continue;
            }

            if (dict[k] != 2 && dict[k] != 4) {
                dict[KING]--;
                if (dict[KING] < 0) {
                    return false;
                }
            }
        }

        return true;
    }

    // 红字 大字 王数量统计
    MaJiangMLHZ.prototype.stats = function(pl, card) {
        var hand = pl.mjhand.slice();
        hand.push(card);

        var redNum = 0;
        var bigNum = 0;
        var kingNum = 0;
        for (var i = 0; i < pl.mjpeng.length; i++) {
            if (this.isRed(pl.mjpeng[i])) {
                redNum += 3;
            }

            if (pl.mjpeng[i] >= 21) {
                bigNum += 3;
            }
        }

        for (var i = 0; i < pl.mjchi.length; i++) {
            var row = pl.mjchi[i].eatCards;
            for (var j = 0; j < 3; j++) {
                if (this.isRed(row[j])) {
                    redNum++;
                }

                if (row[j] >= 21) {
                    bigNum++;
                }
            }
        }

        for (var i = 0; i < hand.length; i++) {
            if (this.isRed(hand[i])) {
                redNum++;
            }

            if (hand[i] != KING && hand[i] >= 21) {
                bigNum++;
            }

            if (hand[i] == KING) {
                kingNum++;
            }
        }

        return {redNum: redNum, bigNum: bigNum, kingNum: kingNum};
    };

    MaJiangMLHZ.prototype.getChiList = function(pl, card) {
        var hand = pl.mjhand.slice();
        hand.push(card);
        var chiList = [];
        for (var i = 20; i < menziList.length; i++) { // 前20为坎 写死20！！
            var row = menziList[i].slice();
            if (row.indexOf(card) >= 0) {
                var flag = true;
                for (var j = 0; j < 3; j++) {
                    if (hand.indexOf(row[j]) < 0) {
                        flag = false;
                        break;
                    }
                }

                if (flag) {
                    var copy = hand.slice();
                    copy.splice(copy.indexOf(row[0]), 1);
                    copy.splice(copy.indexOf(row[1]), 1);
                    copy.splice(copy.indexOf(row[2]), 1);

                    for (var j = 0; j < copy.length; j++) {
                        if (copy[j] != KING && copy[j] != card && pl.canNotPutCard.indexOf(copy[j]) < 0) {
                            chiList.push(row);
                            break;
                        }
                    }
                }
            }
        }

        return chiList;
    };

    MaJiangMLHZ.prototype.canHuBanBan = function(tb, pl) {
        var tData = tb.tData;
        if (!tData.areaSelectMode.isBanBanHu || !pl.isInitHandAllBlack || pl.mjchi.length + pl.mjpeng.length != 0) {
            return false;
        }

        if (tData.areaSelectMode.banBanHuType == 0) {
            if (tData.drawCardIdx == 0 && pl.info.uid == tData.uids[tData.zhuang]) {
                return true;
            }

            if (tData.drawCardIdx == 1 && pl.info.uid != tData.uids[tData.zhuang]) {
                return true;
            }
        } else if (tData.areaSelectMode.banBanHuType == 1) {
            if (pl.isFirstDraw && pl.info.uid == tData.uids[tData.lastDrawPlayer]) {
                return true;
            }
        }

        return false;
    };

    MaJiangMLHZ.prototype.getAchv = function(tb, pl, matrix, be, stats) {
        var tData = tb.tData;
        var redNum = stats.redNum;
        var bigNum = stats.bigNum;
        var kingNum = stats.kingNum;
        matrix = JSON.parse(JSON.stringify(matrix));

        var score = 0;
        var desc = {};
        if (be.length > 0) { // 王替换
            var idx = 0;
            for (var i = 0; i < matrix.length; i++) {
                for (var j = 0; j < matrix[i].length; j++) {
                    if (KING == matrix[i][j]) {
                        matrix[i][j] = be[idx++];
                    }
                }
            }
        }

        var isHuByBanBan = false; // 板板不成牌胡
        if (matrix[matrix.length - 1][0] == 50) {
            isHuByBanBan = true;
        }

        var isHuBySiHuDie = false; // 4蝴蝶不成牌胡
        if (matrix[matrix.length - 1][0] == 60) {
            isHuBySiHuDie = true;
        }

        var redNum_be = 0;
        for (var i = 0; i < be.length; i++) {
            if (this.isRed(be[i])) {
                redNum_be++;
                redNum++;
            }

            if (be[i] >= 21) {
                bigNum++;
            }
        }

        // 蝴蝶飞
        if (kingNum == 4 && tData.areaSelectMode.isHuDieFei) {
            // 除胡别人的蝴蝶 算蝴蝶飞
            if (!(tData.lastPutCard == KING && pl.info.uid != tData.uids[tData.lastDrawPlayer])) {
                score += 20;
                desc.huDieFei = "+20";
            }
        }

        // 板板胡
        if (this.canHuBanBan(tb, pl) && redNum == 0) {
            score += 10;
            desc.banBanHu = "+10";
        }

        if (!isHuByBanBan && !isHuBySiHuDie) {
            var isQiDui = false;
            if (matrix.length == 7) { // 7对
                isQiDui = true;
                for (var i = 0; i < matrix.length; i++) {
                    if (matrix[i].length != 2) {
                        isQiDui = false;
                        break;
                    }

                    if (matrix[i][0] != matrix[i][1]) {
                        isQiDui = false;
                        break;
                    }
                }
            }

            if (isQiDui) {
                score += 20;
                desc.qiDui = "+20";
            }

            if (redNum == 0) { // 黑胡
                score += 20;
                desc.heiHu = "+20";
            } else if (redNum == 1) { // 点胡
                score += 10;
                desc.dianHu = "+10";
            } else if (redNum == 10) { // 十红
                score += 20;
                desc.shiHong = "+20";
            } else if (redNum == 14 && tData.areaSelectMode.isManTangHong) { // 满堂红
                score += 40;
                desc.manTangHong = "+40";
            } else if (redNum == 12 && tData.areaSelectMode.isShiErHong) { // 十二红
                score += 20;
                desc.shiErHong = "+20";
            } else if (redNum == 11 && tData.areaSelectMode.isShiYiHong) { // 十一红
                score += 20;
                desc.shiYiHong = "+20";
            }

            if (bigNum == 0) { // 小一色
                score += 20;
                desc.xiaoYiSe = "+20";
            } else if (bigNum == 14) { // 大一色
                score += 20;
                desc.daYiSe = "+20";
            }

            // 碰碰胡
            if (pl.mjchi.length == 0 && matrix.length <= 5) {
                var isAllPeng = true;
                for (var i = 0; i < matrix.length; i++) {
                    var row = matrix[i];
                    if (row.length == 3) {
                        if (row[0] != row[1] || row[0] != row[2]) {
                            isAllPeng = false;
                            break;
                        }
                    }
                }

                if (tData.areaSelectMode.isSiPeng && isAllPeng && matrix.length == 1) {
                    score += 40;
                    desc.siPengDanDiao = "+40";
                } else if (tData.areaSelectMode.isPengPengHu && isAllPeng) { 
                    score += 20;
                    desc.pengPengHu = "+20";
                }
            }

            // 句句红 或 花胡
            if ((tData.areaSelectMode.isJuJuHong || tData.areaSelectMode.isHuaHu) && matrix.length >= 1 && matrix.length < 7) {
                var isAllOneRed = true;
                var isCanHuaHu = true;
                for (var i = 0; i < pl.mjchi.length; i++) {
                    var row = pl.mjchi[i].eatCards.slice();
                    var redNumInRow = 0;
                    for (var j = 0; j < row.length; j++) {
                        if (this.isRed(row[j])) {
                            redNumInRow++;
                        }
                    }

                    if (redNumInRow != 1) {
                        isAllOneRed = false;
                    }
                    if (redNumInRow < 1) {
                        isCanHuaHu = false;
                        break;
                    }
                }

                for (var i = 0; i < pl.mjpeng.length; i++) {
                    isAllOneRed = false;
                    if (!this.isRed(pl.mjpeng[i])) {
                        isCanHuaHu = false;
                        break;
                    }
                }

                for (var i = 0; i < matrix.length; i++) {
                    var row = matrix[i];
                    var redNumInRow = 0;
                    for (var j = 0; j < row.length; j++) {
                        if (this.isRed(row[j])) {
                            redNumInRow++;
                        }
                    }

                    if ((row.length == 2 && redNumInRow != 2) || (row.length == 3 && redNumInRow != 1)) {
                        isAllOneRed = false;
                    } 
                    if ((row.length == 2 && redNumInRow != 2) || (row.length == 3 && redNumInRow < 1)) {
                        isCanHuaHu = false;
                        break;
                    }
                }

                if (isAllOneRed && tData.areaSelectMode.isJuJuHong) {
                    score += 10;
                    desc.juJuHong = "+10";
                }else if(isCanHuaHu && tData.areaSelectMode.isHuaHu) {
                    score += 10;
                    desc.huaHu = "+10";
                }
            }

            // 一挂匾
            if (tData.areaSelectMode.isYiGuaBian) {
                var bianNum = 0;
                for (var i = 0; i < pl.mjchi.length; i++) {
                    var row = pl.mjchi[i].eatCards.slice();
                    row.sort(function(a, b) {return a - b});
                    if (row.toString() == [2, 7, 10].toString() || row.toString() == [22, 27, 30].toString()) {
                        bianNum++;
                    }
                }

                for (var i = 0; i < matrix.length; i++) {
                    var row = matrix[i];
                    if (row.toString() == [2, 7, 10].toString() || row.toString() == [22, 27, 30].toString()) {
                        bianNum++;
                    }
                }

                if (bianNum == 1 && stats.redNum + redNum_be == 3) {
                    score += 10;
                    desc.yiGuaBian = "+10";
                }
            }
        }

        if (tData.areaSelectMode.isDaHu) {
            if (Object.keys(desc).length > 0) {
                score = 0;
                for (var k in desc) {
                    switch (k) {
                        case "manTangHong":
                            score += 40;
                            break;
                        case "shiErHong":
                            score += 20;
                            break;
                        default:
                            desc[k] = "+10";
                            score += 10;
                            break;
                    }
                }
            }
        }
        
        if (!tData.areaSelectMode.isShuangHe && Object.keys(desc).length >= 2) {
            var desc_max = "";
            var score_max = 0;
            for (var k in desc) {
                var num = Number(desc[k].substr(1));
                if (num > score_max) {
                    score_max = num;
                    desc_max = k;
                }
            }

            for (var k in desc) {
                if (k != desc_max) {
                    delete desc[k];
                }
            }

            score = score_max;
        }

        if (tData.areaSelectMode.isFengDing) { // 封顶80分
            score = Math.min(80, score);
        }

        if (score == 0 && stats.redNum + redNum_be >= tb.tData.minRedNum) {
            score = stats.redNum + redNum_be;
        }

        return {score: score, desc: desc};
    };

    MaJiangMLHZ.prototype.canHu = function(tb, pl, card) {
        var tData = tb.tData;

        if (this.canHuBanBan(tb, pl) && !this.isRed(card)) {
            return true;
        }

        if (card == KING && pl.info.uid != tData.uids[tData.lastDrawPlayer]) {
            return false;
        }

        if (this.isQiDui(pl, card)) {
            return true;
        }

        var hand = pl.mjhand.slice();
        hand.push(card);

        var stats = this.stats(pl, card);
        if (stats.kingNum >= 4 && tData.areaSelectMode.isHuDieFei) {
            return true;
        }

        var hu = function(hand, idx, matrix, be, isDui) {
            if (hand.length == 0) {
                return;
            }

            var list;
            if (hand.length % 3 == 2) { // 取对
                list = duiList;
            } else if (hand.length % 3 == 0) { // 取一句话
                list = menziList;
            }

            if (matrix.length == 1) {
                idx = 0;
            }

            for (var i = idx; i < list.length; i++) {
                var menzi = list[i];
                var copy = hand.slice();
                var flag = true;
                var row = [];
                var be2 = [];
                for (var j = 0; j < menzi.length; j++) {
                    var cd = menzi[j];
                    var idx = copy.indexOf(cd);
                    if (idx >= 0) {
                        copy.splice(idx, 1);
                        row.push(cd);
                    } else {
                        var idx2 = copy.indexOf(KING);
                        if (idx2 >= 0) { // 王牌替代
                            copy.splice(idx2, 1);
                            row.push(KING);
                            be2.push(cd);
                        } else {
                            flag = false;
                            break;
                        }
                    } 
                }

                if (flag) {
                    var hand_copy = hand.slice();
                    var matrix_copy = matrix.slice();
                    var be_copy = be.slice();

                    matrix_copy.push(row);
                    be_copy = be_copy.concat(be2);
                    for (var j = 0; j < row.length; j++) {
                        var cd = row[j];
                        hand_copy.splice(hand_copy.indexOf(cd), 1);
                    }

                    if (hand_copy.length == 0) {
                        // console.log("huMatrix@@ ", matrix_copy);
                        // console.log("be@@ ", be_copy);
                        // console.log(JSON.stringify(this.getAchv(tb, pl, matrix_copy, be_copy, stats)));
                        if (stats.redNum >= tb.tData.minRedNum || this.getAchv(tb, pl, matrix_copy, be_copy, stats).score > 0) {
                            return true;
                        }
                    }

                    if (hu(hand_copy, i, matrix_copy, be_copy)) {
                        return true;
                    }
                }
            }

            return false;
        }.bind(this);
        return hu(hand, 0, [], []);
    };

    MaJiangMLHZ.prototype.getHuInfo = function(tb, pl, card) {
        hand = pl.mjhand.slice();
        hand.push(card);
        var record = card; // 记录
        var maxHuInfo = {
            score: 0,
            matrix: [],
            be: [],
        }
        var tData = tb.tData;
        var stats = this.stats(pl, card);
        // console.log("stats@@ " + JSON.stringify(stats));

        if (this.canHuBanBan(tb, pl) && !this.isRed(record)) { // 板板不成牌胡
            // 这里card已经改了 不是参数card！
            var matrix = this.sortCard(hand); 
            var be = [];
            for (var i = 0; i < stats.kingNum.length; i++) {
                be.push(1);
            }

            matrix.push([50]); // 表示非正常成牌胡！
            var achv = this.getAchv(tb, pl, matrix, be, stats);
            matrix.pop();
            var score = achv.score;
            if (score > maxHuInfo.score) {
                maxHuInfo.score = score;
                maxHuInfo.matrix = matrix;
                maxHuInfo.be = be;
                maxHuInfo.hzdesc = achv.desc;
            }
        }

        if (record == KING && pl.info.uid != tData.uids[tData.lastDrawPlayer]) { // 胡别人的蝴蝶牌 不计算成牌
            var handSort = [];
            for (var i = 0; i < maxHuInfo.matrix.length; i++) {
                var name = maxHuInfo.matrix[i].length == 2 ? "dui" : "chi";
                handSort.push({card: maxHuInfo.matrix[i], name : name});
            }
            maxHuInfo.handSort = handSort;
            delete maxHuInfo.matrix;
            
            return maxHuInfo;
        }

        if (stats.kingNum >= 4 && tData.areaSelectMode.isHuDieFei) { // 四蝴蝶不成牌胡
            var matrix = this.sortCard(hand); 
            var be = [];
            for (var i = 0; i < stats.kingNum.length; i++) {
                be.push(1);
            }

            matrix.push([60]); // 表示非正常成牌胡！
            var achv = this.getAchv(tb, pl, matrix, be, stats);
            matrix.pop();
            var score = achv.score;
            if (score > maxHuInfo.score) {
                maxHuInfo.score = score;
                maxHuInfo.matrix = matrix;
                maxHuInfo.be = be;
                maxHuInfo.hzdesc = achv.desc;
            }
        }
        
        if (this.isQiDui(pl, card)) { // 七对
            var dict = {};
            dict[KING] = 0;
            for (var i = 0; i < hand.length; i++) {
                dict[hand[i]] = dict[hand[i]] ? dict[hand[i]] + 1 : 1;
            }

            var matrix = [];
            var be = [];
            for (var k in dict) {
                var card = Number(k);
                if (card == KING) {
                    continue;
                }

                switch (dict[k]) {
                    case 1:
                        matrix.push([card, KING]);
                        be.push(card);
                        dict[KING]--;
                        break;
                    case 2:
                        matrix.push([card, card]);
                        break;
                    case 3:
                        matrix.push([card, card], [card, KING]);
                        be.push(card);
                        dict[KING]--;
                        break;
                    case 4:
                        matrix.push([card, card], [card, card]);
                        break;
                }
            }

            if (dict[KING] == 0) {
                var achv = this.getAchv(tb, pl, matrix, be, stats);
                var score = achv.score;;
                if (score > maxHuInfo.score) {
                    maxHuInfo.score = score;
                    maxHuInfo.matrix = matrix;
                    maxHuInfo.be = be;
                    maxHuInfo.hzdesc = achv.desc;
                }
            } else if (dict[KING] == 2) {
                var matrix_copy = matrix.slice(); // 二维数组这样拷贝 改变数组中一列的值会影响原二维数组
                matrix_copy.push([KING, KING]);
                for (var i = 0; i < duiList.length; i++) {
                    var be_copy = be.slice();
                    be_copy.push(duiList[i][0], duiList[i][0]);

                    var achv = this.getAchv(tb, pl, matrix_copy, be_copy, stats);
                    var score = achv.score;;
                    if (score > maxHuInfo.score) {
                        maxHuInfo.score = score;
                        maxHuInfo.matrix = matrix_copy;
                        maxHuInfo.be = be_copy;
                        maxHuInfo.hzdesc = achv.desc;
                    }
                }
            } else if (dict[KING] == 4) { // 不用处理,会算做碰碰胡
            }
        }

        var hu = function(hand, idx, matrix, be, isDui) {
            if (hand.length == 0) {
                return;
            }

            var list;
            if (hand.length % 3 == 2) { // 取对
                list = duiList;
            } else if (hand.length % 3 == 0) { // 取一句话
                list = menziList;
            }

            if (matrix.length == 1) {
                idx = 0;
            }

            for (var i = idx; i < list.length; i++) {
                var menzi = list[i];
                var copy = hand.slice();
                var flag = true;
                var row = [];
                var be2 = [];
                for (var j = 0; j < menzi.length; j++) {
                    var cd = menzi[j];
                    var idx = copy.indexOf(cd);
                    if (idx >= 0) {
                        copy.splice(idx, 1);
                        row.push(cd);
                    } else {
                        var idx2 = copy.indexOf(KING);
                        if (idx2 >= 0) { // 王牌替代
                            copy.splice(idx2, 1);
                            row.push(KING);
                            be2.push(cd);
                        } else {
                            flag = false;
                            break;
                        }
                    } 
                }

                if (flag) {
                    var hand_copy = hand.slice();
                    var matrix_copy = matrix.slice();
                    var be_copy = be.slice();

                    matrix_copy.push(row);
                    be_copy = be_copy.concat(be2);
                    for (var j = 0; j < row.length; j++) {
                        var cd = row[j];
                        hand_copy.splice(hand_copy.indexOf(cd), 1);
                    }

                    if (hand_copy.length == 0) {
                        // console.log("huMatrix@@ ", matrix_copy);
                        // console.log("be@@ ", be_copy);
                        // console.log(JSON.stringify(this.getAchv(tb, pl, matrix_copy, be_copy, stats)));

                        var achv = this.getAchv(tb, pl, matrix_copy, be_copy, stats);
                        var score = achv.score;
                        // var score = 0;
                        // if (achv.score > 0) {
                        //     score = achv.score;
                        // } else if (stats.redNum > 3) {
                        //     score = stats.redNum;
                        // }

                        if (score > maxHuInfo.score) {
                            maxHuInfo.score = score;
                            maxHuInfo.matrix = matrix_copy;
                            maxHuInfo.be = be_copy;
                            maxHuInfo.hzdesc = achv.desc;
                        }
                    }

                    hu(hand_copy, i, matrix_copy, be_copy);
                }
            }
        }.bind(this);
        hu(hand, 0, [], []); // 构成胡牌组合胡

        var handSort = [];
        for (var i = 0; i < maxHuInfo.matrix.length; i++) {
            var name = maxHuInfo.matrix[i].length == 2 ? "dui" : "chi";
            handSort.push({card: maxHuInfo.matrix[i], name : name});
        }
        maxHuInfo.handSort = handSort;
        delete maxHuInfo.matrix;

        return maxHuInfo;
    };

    module.exports = MaJiangMLHZ;

    function test() {
        var a = new MaJiangMLHZ();
        var tb = {
            tData: {
                minRedNum: 3, 
                areaSelectMode : {
                    maxPlayer: 2,
                    payWay: 0,
                    convertible: false,
                    minRedNum: 3,
                    kingNum: 0,
                    isShuangHe: true,
                    isPengPengHu: true,
                    isYiGuaBian: true,
                    isManTangHong: true,
                    isHuDieFei: true,
                    isSiPeng: true,
                    isDaHu: true,
                    isBanBanHu: true,
                    isJuJuHong: true,
                    isShiErHong: true,
                    isShiYiHong: true,
                    isFengDing: true,
                    isMaiPai: true,
                    banBanHuType: 0,
                    isManualCutCard: false,
                    fanBei: 0,
                    fanBeiScore: 10,
                    jieSuanDiFen: 10,
                    isRandomZhuang: true,
                    isHuaHu: true
                },
                minRedNum: 3,
                gameCnName: "ãèÂÞºì×Ö",
                hasReadyBtn: true,
                currCard: 5,
                drawType: 0,
                roundNum_play: 1,
                isFanBei: false,
                isLastDraw: false,
                drawCardIdx: 6,
                isFirstPut: false,
                hasPay: true
        }
    };
        var tData = tb.tData;
        tData.putType = 1;
        tData.uids = [1];
        tData.lastDrawPlayer = 0;
         
        var pl =  {
                mjhand: [7,
                        25,
                        27,
                        28,
                        29,
                        30,
                        26],
                tableMsg: [100],
                roomStatistics: [10, 1, 0],
                lastOffLineTime: null
            };
        // pl.info = {uid: 1};
        // var hand = [1, 1, 4, 4,5,5,8, 24, 24, 27, 3, 91, 91];
        // pl.mjhand = hand;
        pl.mjpeng = [22,23];
        pl.mjchi = [{eatCards:[6,7,8]}];
        // // pl.mjchi = [
        // // {
        // //     eatCards: [7, 2, 10]
        // // }];
        // pl.mjchi = [];
        // pl.canNotPutCard = [3, 4, 9, 5];
        // console.log(JSON.stringify(a.stats(pl, 2)));
        // console.log(a.canHu(tb, pl, 8));
        // console.log(a.getChiList(pl, 22));
        // console.log(a.sortCard(hand));
        // a.canHu(tb, pl, 5);
        // console.log(pl.hzdesc);
    }
    // test();
}());