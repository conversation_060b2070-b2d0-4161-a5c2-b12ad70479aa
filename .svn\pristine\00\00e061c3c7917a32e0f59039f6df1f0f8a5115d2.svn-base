function GameCodeXiangXiangPHZ(majiang, app) {
    this.majiang = majiang;
    this.app = app;
    this.init();
}

GameCodeXiangXiangPHZ.prototype.init = function() {
    this.__debug = false;

    var eatPriority = {};
    eatPriority.WangDiao = 14; // 王钓/单王钓  跑/提后 其他成门子, 只剩下一扎张王
    eatPriority.WangDiaoWang = 13; // 王钓王      单王钓 的时候 又拿到一张王
    eatPriority.WangChuang = 12; // 王闯/双王钓 (手上有2张王) 其他牌成牌后, 只剩下两张王
    eatPriority.WangChuangWang = 11; // 王闯王      双王钓的时候 又拿到一张王
    eatPriority.WangZha = 10; // 王炸       手上有3张王
    eatPriority.HuPai = 7; // 胡牌
    eatPriority.TiPai = 6; // 提牌 暗杠
    eatPriority.PaoPai = 5; // 跑牌 明杠
    eatPriority.WeiPai = 4; // 偎牌 暗碰
    eatPriority.PengPai = 3; // 碰牌
    eatPriority.ChiPai = 2; // 吃牌
    eatPriority.GuoPai = 1; // 过牌
    this.EatPriority = eatPriority;

    var eatFlags = {};
    eatFlags.hu = 32; // 胡
    eatFlags.ti = 16; // 提
    eatFlags.wei = 8; // 偎
    eatFlags.pao = 4; // 跑
    eatFlags.peng = 2; // 碰
    eatFlags.chi = 1; // 吃
    eatFlags.wu = 0; // 无

    // 打log用
    eatFlags[eatFlags.hu] = '胡32';
    eatFlags[eatFlags.ti] = '提16';
    eatFlags[eatFlags.wei] = '偎8';
    eatFlags[eatFlags.pao] = '跑4';
    eatFlags[eatFlags.peng] = '碰2';
    eatFlags[eatFlags.chi] = '吃1';
    eatFlags[eatFlags.wu] = '无';
    this.EatFlags = eatFlags;

    // 自动执行
    var autoEat = {};
    autoEat[eatFlags.ti] = eatPriority.TiPai;
    autoEat[eatFlags.pao] = eatPriority.PaoPai;
    autoEat[eatFlags.wei] = eatPriority.WeiPai;
    this.AutoEat = autoEat;
    this.allAutoEatFlag = eatFlags.ti + eatFlags.pao + eatFlags.wei;

    // 操作 对应 优先级 的转换
    var flagToPriority = {};
    flagToPriority[eatFlags.hu] = eatPriority.HuPai; // 胡
    flagToPriority[eatFlags.ti] = eatPriority.TiPai; // 提
    flagToPriority[eatFlags.pao] = eatPriority.PaoPai; // 跑
    flagToPriority[eatFlags.wei] = eatPriority.WeiPai; // 偎
    flagToPriority[eatFlags.peng] = eatPriority.PengPai; // 碰
    flagToPriority[eatFlags.chi] = eatPriority.ChiPai; // 吃
    flagToPriority[eatFlags.wu] = 0; // 无
    this.FlagToPriority = flagToPriority;

    // 打出的牌的状态  
    var lastPutCardState = {};
    lastPutCardState.moPutCard = 1; // 摸牌后打出的牌
    lastPutCardState.eatPutCard = 2; // 吃牌后打出的牌
    lastPutCardState.eatCard = 3; // 被吃碰杠的牌 例:打出被碰了的牌的状态
    this.PutCardState = lastPutCardState;
};

GameCodeXiangXiangPHZ.prototype.debuglog = function(pl, msg, session, next, tb) {
};

//玩家创建房间的时候，初始化的数据
GameCodeXiangXiangPHZ.prototype.initPlayerOnce = function(pl, tb) {
    pl.mjwei = [];
    pl.jiachuiNum = -2; // -2无加锤选项 -1等待加锤 0不加锤  1加锤
    if (tb.tData.areaSelectMode.isJiaChui) {
        pl.jiachuiNum = -1;
    }
    pl.roomStatistics = [0, 0, 0, 0, 0]; // 胡牌 自摸 点炮 提 跑次数统计

    var tData = tb.tData;
    tData.trustEnd = -1; // 等操作玩家 进入托管状态倒计时
};

GameCodeXiangXiangPHZ.prototype.initPlayer = function(pl) {
    pl.mjwei = []; //偎牌
    pl.mjHide = []; // 隐藏的提、偎牌
    pl.mjdesc = [];
    pl.hzdesc = {};
    pl.mjsort = []; //桌面上的牌的位置顺序
    pl.handSort = [];
    pl.isQiHu = false;      // 是否弃胡
    pl.skipChi = [];        // 过吃的牌
    pl.skipPeng = [];       // 过碰的牌
    pl.putCount = 0;        // 出牌数量 吃碰杠后打出的牌 + 摸牌后打出的牌
    pl.jiazhuNum = -1;       // 举手标志
    pl.longCard = [];       // 垅和提
    pl.autoHu = false; // 跑后自动胡标志
    pl.isWeiDead = false; // 偎牌后死手
    pl.skipHuWinone = 0; // 过点炮胡分
    pl.isHuByHand = false; // 手牌胡
    pl.isBlockEat = false; // 放跑不能吃碰
    pl.canNotPutCard = []; // 不能打的牌（吃边打边规则）
    pl.limitHuPutCard = []; // 打后限胡的牌 （吃边打边规则）
    pl.limitHuTypeList = []; // 限胡类型列表
    pl.isPutCardOnce = false; // 打过牌标志
    pl.startTiNum = -1;
    pl.tiLong = false;
    pl.mjPutCount = 0;
    pl.trust = false;        // 是否在托管状态
    pl.trustEnd = -1;        // 进入托管状态倒计时
};

// 开始切牌
GameCodeXiangXiangPHZ.prototype.startShuffleCards = function(tb) { 
    var tData = tb.tData;  
    tData.tState = TableState.waitShuffle;
    tData.shuffler = tData.zhuang;
    if (tData.roundNum != tData.roundAll) {
        tData.shuffler = (tData.zhuang - 1 + tData.maxPlayer) % tData.maxPlayer;
    }  
    tb.NotifyAll("startShuffleCards", {curPlayer: tData.shuffler});
};

// 切牌结束 
GameCodeXiangXiangPHZ.prototype.endShuffleCards = function(pl, msg, session, next, tb) { 
    var tData = tb.tData;  
    if (tData.tState != TableState.waitShuffle || pl.uid != tData.uids[tData.shuffler]) {
        return;
    }
    tData.tState = TableState.waitCard;  
    tb.NotifyAll("endShuffleCards", msg); 
    tb.runStartGame(); 
};

GameCodeXiangXiangPHZ.prototype.collectPlayer = function(tb) {
    var ptyList = [
        'info', 'eatFlag', 'mjState', 'mjwei', 'mjpeng', 'mjgang0', 'mjgang1', 'mjchi', 'mjchiCard', 'mjput', 'isQiHu',
        'onLine', 'skipPeng', 'delRoom', 'isNew', 'winall', 'linkZhuang', 'pengchigang', 'jiazhuNum', 'canNotPutCard',
        'limitHuPutCard', 'mjHide', 'mjsort', 'tableMsg', 'locationMsg', 'roomStatistics', 'lastOffLineTime', 'jiachuiNum',
        'freeBegin','trust'
    ];

    return tb.collectPlayer.apply(tb, ptyList);
};

GameCodeXiangXiangPHZ.prototype.doBeforeHands = function(tb) {
    tb.AllPlayerEatSelect = {};
    // 所有玩家可执行的操作
    tb.AllPlayerEatFlags = {}; //   key=pl.uid value=eatFlag
    // 记录 所有打出的牌
    tb.AllOutCards = [];  // { '1':{value, uid, state = PutCardState } }
    if(tb.tData.areaSelectMode.maipai && tb.tData.maxPlayer == 2)
        tb.tData.huangNum = parseInt(tb.tData.areaSelectMode.maiPaiType) * 10;
    else
        tb.tData.huangNum = 0;

    this.majiang.checkHandCards(tb);
};

GameCodeXiangXiangPHZ.prototype.mjJiazhu = function(pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState != TableState.waitJiazhu || pl.jiachuiNum != -1) {
        return;
    }

    if (msg.jiachuiNum != 0 && msg.jiachuiNum != 1) {
        return;
    }

    pl.jiachuiNum = msg.jiachuiNum;
    tb.NotifyAll("MJJiazhu", {jiachuiNum: pl.jiachuiNum, uid: pl.uid});

    if (tb.AllPlayerCheck(function(p) {return p.jiachuiNum != -1;})) {
        if(tData.areaSelectMode.isManualCutCard){ // 切牌
            this.startShuffleCards(tb);
        }else{
            tb.runStartGame();
        } 
    }
};

// 庄家摸最后一张手牌 亮张
GameCodeXiangXiangPHZ.prototype.drawLastCard = function(tb) { // todo 改天胡判断
    var tData = tb.tData;
    tData.isLastDraw = true; // 当前牌是否为亮张标志
    tData.isPicked = false; // 亮张是否已入手
    tb.AllPlayerRun(function(p) {
        p.mjState = TableState.waitCard;
    });
    tData.tState = TableState.waitCard;

    this.setNextPutCardPlayer(tData.uids[tData.zhuang], tb);
    this.sendNewCard(tb);
};

// 庄家将亮张 收入手牌
GameCodeXiangXiangPHZ.prototype.pickLastCard = function(tb, pl) {
    var tData = tb.tData;
    var card = this.getLastPutCardData('value', tb);

    if(!tData.isLastDraw && (pl.eatFlag & this.EatFlags.ti || pl.eatFlag & this.EatFlags.wei || pl.eatFlag & this.EatFlags.pao))
    {
        if(pl.tiLong){
            return;
        }
    }else if(pl.startTiNum > 0 || tData.isLastDraw){
        pl.mjhand.push(card);
    }

    if(tData.isLastDraw){
        tb.AllPlayerRun(function (p) {
            if(p.uid != tData.uids[tData.zhuang])
            {
                var longList = [];
                var cardNum = {};
                for (var i = 0; i < p.mjhand.length; i++) {
                    var card = p.mjhand[i];
                    cardNum[card] = cardNum[card] ? cardNum[card] + 1 : 1;
                    if(cardNum[card] == 4){
                        longList.push(card);
                    }
                }
                p.startTiNum = longList.length - 1;
            }
        });
    }

    if(tData.isLastDraw || pl.startTiNum > 0){
        tData.currCard = -1;
        pl.mjput.pop();
        tb.NotifyAll("HZPickCard", {card: card, uid: pl.uid});
        cloneDataAndPush(tb.mjlog, "HZPickCard", {card: card, uid: pl.uid});
    }else{
        pl.startTiNum--;
    }
    this.tiLong(tb, pl, true);

    if (tData.areaSelectMode.trustTime != -1) {
        tData.tState = TableState.waitPut;
        pl.mjState = TableState.waitPut;
        this.checkTrust(tb, pl);
    }

    if(tData.zhuang == tData.uids.indexOf(pl.uid) && tData.isLastDraw){
        this.checkRaise(tb);
    }
};

// 提垅
GameCodeXiangXiangPHZ.prototype.tiLong = function(tb, pl,isFirst) {
    if(pl.tiLong){
        return;
    }
    var tData = tb.tData;
    var longList = [];
    var cardNum = {};
    for (var i = 0; i < pl.mjhand.length; i++) {
        var card = pl.mjhand[i];
        cardNum[card] = cardNum[card] ? cardNum[card] + 1 : 1;
        if(cardNum[card] == 4){
            longList.push(card);
        }
    }
   
    for (var i = 0; i< longList.length; i++) {
        var card = longList[i];
        this.doGangCard(pl, 1, tb, card);
    }

    var addNum = longList.length;
    if (isFirst) {
        addNum -= 1;
    }

    if (addNum <= 0) {
        pl.tiLong = true;
        return;
    }
    // 补牌 庄家提牌可以一次性补完所以得牌的
    if(tData.uids[tData.zhuang] != pl.uid)
        return;

    var cardList = [];
    for (var i = 0; i < addNum; i++) {
        var card = tb.cards[tData.cardNext++];
        pl.mjhand.push(card);
        cardList.push(card);
    }

    pl.notify("HZAddCards", {cardList: cardList, uid: pl.uid});
    cloneDataAndPush(tb.mjlog, "HZAddCards", {cardList: cardList, uid: pl.uid});

    tb.NotifyAll("HZCardNum", {cardNext: tData.cardNext});
    cloneDataAndPush(tb.mjlog, "HZCardNum", {cardNext: tData.cardNext});

    this.tiLong(tb, pl, false);
};

// 庄家打牌或天胡
GameCodeXiangXiangPHZ.prototype.startPutCard = function(tb) {
    var tData = tb.tData;
    var pl = this.getPlayerByIndex(tData.zhuang, tb);

    this.setPlayerState(pl, TableState.waitPut);
    this.setTableState(TableState.waitPut, tb);
    if (this.majiang.canHu(tb, pl, null).huMatrix.length > 0) { // 天胡判断
        this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.hu, tb);
        this.setTableState(TableState.waitEat, tb);
        this.setPlayerState(pl, TableState.waitEat);
    }
};

GameCodeXiangXiangPHZ.prototype.checkRaise = function(tb) {
    var isWaitJushou = false;
    if (!isWaitJushou) {
        this.startPutCard(tb);
    }

    var jiazhuNums = {};
    tb.AllPlayerRun(function(p){
        jiazhuNums[p.uid] = p.jiazhuNum;
    });


    var zhuangPl = this.getPlayerByIndex(tb.tData.zhuang, tb);
    var jiazhuMsg = {jiazhuNums: jiazhuNums, eatFlag: zhuangPl.eatFlag, mjState: zhuangPl.mjState};
    var autoHuUid = this.getAutoHuUid(tb);
    if (autoHuUid) {
        jiazhuMsg.eatFlag = 0;
    }
    tb.NotifyAll("HZCheckRaise", jiazhuMsg);
    cloneDataAndPush(tb.mjlog, "HZCheckRaise", jiazhuMsg);


    this.checkAutoHu(autoHuUid, tb);
};

//改变当前玩家
GameCodeXiangXiangPHZ.prototype.doAfterHands = function(tb) {
    var tData = tb.tData;
    tData.curPlayer = tData.zhuang;
};

GameCodeXiangXiangPHZ.prototype.updateClientScene = function(pl, tb) {
    if (tb.initSceneData && pl){
        pl.notify("initSceneData", tb.initSceneData(pl));
    }else{
        tb.AllPlayerRun(function(pl){
            pl.notify("initSceneData", tb.initSceneData(pl));
        }.bind(this));
    }
};

GameCodeXiangXiangPHZ.prototype.cleanObject = function(object) {
    if (typeof(object) == 'object') {
        for (var i in object) {
            delete object[i];
        }
    }
};

// 桌子状态
// waitJoin: 1,//等待加入    waitReady: 2,//准备    waitPut: 3,//等待出牌
// waitEat: 4,//等待胡杠碰吃 waitCard: 5,//等待发牌  roundFinish: 6,//一局结束
// isReady: 7, //准备       waitJiazhu: 8, //每局发牌前等待加注（东海）
GameCodeXiangXiangPHZ.prototype.setTableState = function(pTableState, tb) {
    tb.tData.tState = pTableState;
};

// 判断桌子当前状态
GameCodeXiangXiangPHZ.prototype.isTableState = function(pTableState, tb) {
    return tb.tData.tState == pTableState;
};

GameCodeXiangXiangPHZ.prototype.setPlayerState = function(pl, pState) {
    pl.mjState = pState;
};

GameCodeXiangXiangPHZ.prototype.isPlayerEatFlag = function(pl, pEatFlag) {
    return pl.eatFlag & pEatFlag;
};

GameCodeXiangXiangPHZ.prototype.isPlayerState = function(pl, pState, tb) {
    return pl.mjState == pState;
};

GameCodeXiangXiangPHZ.prototype.getPlayerIndex = function(pl, tb) {
    var playerIndex = tb.tData.uids.indexOf(pl.uid);
    return playerIndex;
};

// 改变玩家属性
GameCodeXiangXiangPHZ.prototype.setPlayerProperty = function(uid, valueName, value, tb) {
    if ('eatFlag' == valueName) {
        var flagStr = '';
        for (var tempFlag in this.EatFlags) {
            var flag = parseInt(tempFlag);
            if (flag && (value & flag)) {
                flagStr = flagStr + ' ' + this.EatFlags[flag];
            }
        }
    }
    tb.getPlayer(uid)[valueName] = value;
};

/**
 * 删除玩家的牌 对 mjhand mjput 等操作
 * @param  {object} pl 玩家数据对象
 * @param  {array} delArr 需要删除的手牌  [8, 8 , 8, 2 ] = 删除3张8 1张2
 * @return {bool} 是否删除成功
 */
GameCodeXiangXiangPHZ.prototype.deleteCard = function(pl, arrName, delArr) {
    var isHadDelCard = true;
    var arr = pl[arrName];
    for (var i in delArr) {
        if (arr.indexOf(delArr[i]) < 0)
            isHadDelCard = false;
    }

    if (isHadDelCard) {
        for (var i in delArr) {
            arr.splice(arr.indexOf(delArr[i]), 1);
        }
    }

    return isHadDelCard;
};

/**
 * 设置摸牌出牌的玩家/ sendNEwCard给这个玩家 /同类操作优先级最高的玩家
 * @param {int} playerIndex 玩家在uid
 */
GameCodeXiangXiangPHZ.prototype.setNextPutCardPlayer = function(uid, tb) {
    var playerIndex = 0;
    if (uid == null || typeof(uid) == 'undefined') {
        playerIndex = this.getPutCardPlayerIndex(tb);
        playerIndex = (playerIndex + 1) % tb.tData.maxPlayer;
    } else {
        playerIndex = tb.tData.uids.indexOf(uid)
    }
    tb.tData.curPlayer = playerIndex;
};

// 判断 playerUid 是否为当前出牌玩家
GameCodeXiangXiangPHZ.prototype.isPutCardPlayer = function(playerUid, tb) {
    var boolValue = tb.tData.uids[tb.tData.curPlayer] == playerUid;
    return boolValue;
};

GameCodeXiangXiangPHZ.prototype.getPutCardPlayerIndex = function(tb) {
    return tb.tData.curPlayer;
};

// 获取当前操作的玩家
GameCodeXiangXiangPHZ.prototype.getPutCardPlayer = function(tb) {
    var uid = tb.tData.uids[this.getPutCardPlayerIndex(tb)];
    return tb.getPlayer(uid);
};

GameCodeXiangXiangPHZ.prototype.getPlayerByUid = function(uid, tb) {
    return tb.getPlayer(uid);
};

GameCodeXiangXiangPHZ.prototype.getPlayerByIndex = function(index, tb) {
    return tb.getPlayer(tb.tData.uids[index]);
};

/**
 * 记录打出的牌
 * @param {[type]} pLastPutCard 记录打出的牌
 * @param {[type]} outCardState 打出的牌的状态
 * @param {[type]} pl 玩家数据对象
 */
GameCodeXiangXiangPHZ.prototype.setLastPutCard = function(pLastPutCard, outCardState, pl, tb) {
    // 如果 吃碰杠后 从手牌打出的牌
    if(outCardState == this.PutCardState.eatPutCard){
        this.deleteCard(pl, 'mjhand', [pLastPutCard])
    }
    pl.mjput.push(pLastPutCard);
    pl.putCount++;

    var cardData = {};
    cardData.uid = pl.uid;
    cardData.value = pLastPutCard;
    cardData.state = outCardState;
    tb.tData.lastPutCard = pLastPutCard;
    tb.AllOutCards.push(cardData);
};

GameCodeXiangXiangPHZ.prototype.getLastPutCardData = function( keyname, tb ){
    if (tb.AllOutCards.length > 0) {
        var cardData = tb.AllOutCards[tb.AllOutCards.length - 1];
        return cardData[keyname];
    } else {
        return null;
    }
};

GameCodeXiangXiangPHZ.prototype.setLastPutCardState = function(cardState, tb) {
    var cardData = tb.AllOutCards[tb.AllOutCards.length - 1];
    if( cardData ) cardData.state = cardState;
};

GameCodeXiangXiangPHZ.prototype.setLastPutCardPlayerIndex = function(pLastPutCardPlayer, tb) {
    tb.tData.lastPlayer = pLastPutCardPlayer;
};

GameCodeXiangXiangPHZ.prototype.getLastPutCardPlayerIndex = function(tb) {
    return tb.tData.lastPlayer;
};

GameCodeXiangXiangPHZ.prototype.getLastPutCardPlayer = function(tb) {
    var uid = tb.tData.uids[this.getLastPutCardPlayerIndex(tb)];
    return tb.getPlayer(uid);
};

/**
 * 更新所有玩家可执行的操作，一般用来通知客户端玩家显示什么按钮
 * @param {int} pNewCard 玩家操作的牌， 摸到的牌/打出的牌 lastPutCard
 * @param {array} pEatFlags 记录玩家可执行的操作
 * @param {object} pIgnoreEats 忽略玩家操作  { uid:eatflag, uid:eatflag }
 * @return {int} 返回是否有玩家需要执行操作
 */
GameCodeXiangXiangPHZ.prototype.updateAllPlayerEatFlag = function(pNewCard, pEatFlags, pIgnoreEats, tb) {
    var hadEatFlag = false;
    pNewCard = pNewCard || 0;
    pEatFlags = pEatFlags || tb.AllPlayerEatFlags;
    pIgnoreEats = pIgnoreEats || [];

    var isHadAutoDo = false;
    var autoDoFlag = 0;
    var tmpEatFlags = {};
    var checkAllPlayer = function(pl) {
        var eatFlag = this.getEatFlag(pl, pNewCard, tb);
        tmpEatFlags[pl.uid] = eatFlag;
        if(this.allAutoEatFlag & eatFlag){
            isHadAutoDo = true;
            autoDoFlag = eatFlag;
        }
    }.bind(this);
    tb.AllPlayerRun(checkAllPlayer);

    for(var uid in tmpEatFlags){
        var pl = this.getPlayerByUid(uid, tb);
        var ignoreEat = pIgnoreEats[uid];
        var eatFlag = tmpEatFlags[uid];
        // 如果有需要忽略的操作 ， 过滤掉
        if(ignoreEat){
            eatFlag = (eatFlag - (eatFlag & ignoreEat));
        }
        // 如果有自动执行的操作， 过滤吃，碰操作
        if(isHadAutoDo){
            eatFlag = (eatFlag - (eatFlag & this.EatFlags.chi) - (eatFlag & this.EatFlags.peng));
            // 有提 偎去掉胡
            if ((autoDoFlag&this.EatFlags.ti) > 0 || (autoDoFlag&this.EatFlags.wei) > 0) {
                eatFlag = eatFlag - (eatFlag&this.EatFlags.hu);
            }
        }
        this.setPlayerProperty(uid, 'eatFlag', eatFlag, tb);
        if (eatFlag > 0) {
            this.setPlayerState(pl, TableState.waitEat);
            pEatFlags[uid] = eatFlag;
            hadEatFlag = true;
        } else {
            pEatFlags[uid] = 0;
        }
    }

    return hadEatFlag;
};

/**
 * 获取玩家操作的优先级
 * @param  {object} pl 玩家数据对象
 * @param  {int} pEatPriority 吃碰杠胡的优先级 如果不需要操作，pEatPriority=0
 * @return {int} 返回玩家操作的优先级
 */
GameCodeXiangXiangPHZ.prototype.getPlayerDoPriority = function(pl, pEatPriority, tb) {
    if (0 == pEatPriority) return 0;
    var playerNum = tb.tData.maxPlayer;
    var curPlayerIndex = this.getPutCardPlayerIndex(tb);
    var plPlayerIndex = this.getPlayerIndex(pl, tb);
    var playerPriority = 0;
    if (curPlayerIndex > plPlayerIndex) playerPriority = curPlayerIndex - plPlayerIndex;
    if (curPlayerIndex < plPlayerIndex) playerPriority = playerNum + curPlayerIndex - plPlayerIndex;
    if (curPlayerIndex == plPlayerIndex) playerPriority = playerNum;
    var doPriority = pEatPriority * 10 + playerPriority;
    return doPriority;
};

/**
 * 这是玩家执行了的操作， 无操作的玩家默认优先级最低
 * @param {object} pl 玩家数据对象
 * @param {int} pEatPriority 操作类型对应的优先级/吃碰杠胡等优先级
 * @param {int} pEatFlag 玩家所有可以选择的操作， 用来计算是否 过胡过碰过吃
 * @param {function} doEatSelectCallBack 执行玩家操作时调用的回调
 * @param {int} delayTime 执行延时 (单位/毫秒)
 */
GameCodeXiangXiangPHZ.prototype.setPlayerEatSelect = function(pl, pEatPriority, pEatFlag, doEatSelectCallBack, tb) {
    var mAllPlayerEatSelect = tb.AllPlayerEatSelect;
    var pDelayTime = 0;
    if (!mAllPlayerEatSelect[pl.uid]) {
        var doPriority = this.getPlayerDoPriority(pl, pEatPriority, tb);
        var maxPrioritySelect = {
            priority: doPriority,
            eatFlag: pEatFlag,
            callback: doEatSelectCallBack,
            delayTime: pDelayTime || 0
        };
        mAllPlayerEatSelect[pl.uid] = maxPrioritySelect;
    }
    // console.log("mAllPlayerEatSelect2@@ " + JSON.stringify(mAllPlayerEatSelect));
};

/**
 * 是否能执行 优先级最高的 玩家操作`
 * 如果是 优先级最高 玩家操作， 不用等待其他操作
 * @return {Boolean}
 */
GameCodeXiangXiangPHZ.prototype.isCanDoEatSelect = function(tb) {
    var mAllPlayerEatSelect = tb.AllPlayerEatSelect;
    var mAllPlayerEatFlags = tb.AllPlayerEatFlags;

    // 执行了操作的玩家数量
    var doSelectCount = Object.keys(mAllPlayerEatSelect).length;
    var maxSelectCount = 0;
    for (var i in mAllPlayerEatFlags) {
        if (mAllPlayerEatFlags[i] > 0)
            maxSelectCount++;
    }

    // 如果所有玩家都执行了选择(点击前端 吃碰杠按钮) 可以执行
    if (maxSelectCount == doSelectCount)
        return true;


    // 计算 已执行的操作中 最高优先级的操作
    var maxEatSelectUid = null;
    var maxEatSelectPriority = 0;
    for (var uid in mAllPlayerEatSelect) {
        if (!maxEatSelectUid || mAllPlayerEatSelect[maxEatSelectUid].priority < mAllPlayerEatSelect[uid].priority)
            maxEatSelectUid = uid;
    }

    if (maxEatSelectUid) maxEatSelectPriority = mAllPlayerEatSelect[maxEatSelectUid].priority

    // 计算未操作的玩家可执行的 最大优先级
    var maxEatFlagForUid = function(uid) {
        var flag = mAllPlayerEatFlags[uid];
        flag = flag - (flag & this.EatFlags.pao);
        if (flag > 0) {
            // 将二进制最大位 转换成 十进制数值
            flag = Math.pow(2, flag.toString(2).length - 1);
        } else {
            flag = 0;
        }
        var eatPriority = this.FlagToPriority[flag];
        var pl = tb.getPlayer(uid);
        var doPriority = this.getPlayerDoPriority(pl, eatPriority, tb);
        return doPriority;
    }.bind(this);

    // 获取 未操作玩家中，最高优先级的玩家uid
    var maxEatFlagUid = null;
    var maxEatFlagUidPriority = 0;
    for (var uid in mAllPlayerEatFlags) {
        if (!mAllPlayerEatSelect[uid]) {
            if (!maxEatFlagUid || maxEatFlagForUid(maxEatFlagUid) < maxEatFlagForUid(uid))
                maxEatFlagUid = uid;
        }
    }

    if (maxEatFlagUid) maxEatFlagUidPriority = maxEatFlagForUid(maxEatFlagUid);

    // 判断 玩家是否 选择最高优先级的操作
    var isDoMaxSelect = maxEatSelectPriority >= maxEatFlagUidPriority;
    return isDoMaxSelect;
};

// 执行优先级最高的玩家操作
GameCodeXiangXiangPHZ.prototype.doPlayerEatSelect = function(tb) {
    // 如果所有玩家都操作了， 执行优先级最高的玩家的操作
    var isCanDoEatSelect = this.isCanDoEatSelect(tb);
    var mAllPlayerEatSelect = tb.AllPlayerEatSelect;

    if (isCanDoEatSelect) {
        var maxPrioritySelect = null;
        for (var uid in mAllPlayerEatSelect) {
            if (!maxPrioritySelect || maxPrioritySelect.priority < mAllPlayerEatSelect[uid].priority) {
                maxPrioritySelect = mAllPlayerEatSelect[uid];
            }
        }
        // 把eatFlag置0放在执行 操作之后(过胡自动跑 需要eatFlag);
        // 重置所有玩家的操作为0 
        tb.AllPlayerRun(function(pl) {
            this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);
        }.bind(this));
        // delayTime毫秒后， 执行优先级最高的玩家操作
        maxPrioritySelect.callback();
        // 清空 记录玩家选择的操作列表
        this.cleanObject(mAllPlayerEatSelect);
    }
};

// 检测是否有自动执行的操作， 有则自动执行
GameCodeXiangXiangPHZ.prototype.checkAutoDoEatSelect = function(pAllPlEatFlag, tb) {
    var mDoPriority = 0;
    var mAutoPlayer = null;
    var mAutoFlag = 0;
    var allPlEatSelect = tb.AllPlayerEatSelect;
    pAllPlEatFlag = pAllPlEatFlag || tb.AllPlayerEatFlags;

    for(var uid in pAllPlEatFlag){
        var playerEatFlag = pAllPlEatFlag[uid];
        var pl = this.getPlayerByUid(uid, tb);

        for(var autoEatFlag in this.AutoEat){
            var eatPriority = this.AutoEat[autoEatFlag];
            var doPriority = this.getPlayerDoPriority(pl, eatPriority, tb);
            // 如果有玩家执行了可以胡， 但是还没操作，则不能自动偎提跑
            if((this.EatFlags.hu & playerEatFlag) && !allPlEatSelect[uid]) {
                return false;
            }
            if( (playerEatFlag & autoEatFlag) && mDoPriority < doPriority ){
                mDoPriority = doPriority;
                mAutoFlag = autoEatFlag;
                mAutoPlayer = pl;
            }
       
        }
    }

    if(mAutoPlayer && mAutoFlag > 0){
        // 执行优先级最高的玩家操作后， 重置所有玩家的操作为0
        tb.AllPlayerRun(function(pl) {
            this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);
        }.bind(this));
        
        // 清空 记录玩家选择的操作列表
        this.cleanObject(allPlEatSelect);

        if(mAutoFlag == this.EatFlags.ti)
            this.doGangCard(mAutoPlayer, 1, tb, false, true);
        if(mAutoFlag == this.EatFlags.pao)
            this.doGangCard(mAutoPlayer, 2, tb, false, true);
        if(mAutoFlag == this.EatFlags.wei)
            this.doWeiCard(mAutoPlayer, tb, true);

        return true;
    }
};

// 获取总的牌的数量
GameCodeXiangXiangPHZ.prototype.getCardTotalNum = function() {
    return 80;
};

/**
 * 获取玩家可执行的操作
 * @param  {object} pl 玩家对象
 * @param  {int} newCard 摸到的牌/新发的牌/别人打出的牌 
 * @return {int} 二进制各位对应不同操作  32胡 16提 8偎 4跑 2碰 1吃 0无需操作
 */
GameCodeXiangXiangPHZ.prototype.getEatFlag = function(pl, newCard, tb) {
    var eatFlag = this.EatFlags.wu;
    var tData = tb.tData;

    if (tData.isLastDraw) { // 亮张
        return eatFlag;
    }

    if(pl.startTiNum <= 0){
        var canHuInfo = this.majiang.canHu(tb, pl, newCard);
        if (!pl.isWeiDead && canHuInfo.huMatrix.length > 0 && tData.putType != 0) {
            eatFlag += this.EatFlags.hu;
        }
    }

    if (0 < newCard ) {
        var cardCount = this.majiang.cardHandCount(pl.mjhand, newCard);
        
        // 提牌(手牌3张或者偎牌)
        if(this.isPutCardPlayer(pl.uid, tb) && (cardCount == 3 || pl.mjwei.indexOf(newCard)>=0) )
        {
            eatFlag += this.EatFlags.ti;
        }

        //偎牌
        if (this.isPutCardPlayer(pl.uid, tb) && cardCount == 2) {
            eatFlag += this.EatFlags.wei;
        }

        //跑牌 (杠别人打出的牌, 手上有偎和3张牌能跑 或者 摸到自己碰过的牌)
        var currUid = tData.uids[tData.curPlayer];
        var curPlayer = tb.getPlayer(currUid);
        var isMoPutCard = this.getLastPutCardData('state', tb) == this.PutCardState.moPutCard;
        var canGangWei = !this.isPutCardPlayer(pl.uid, tb) && this.majiang.canPao(pl, newCard);
        var canGangPeng =this.majiang.canPao(pl, newCard, isMoPutCard);
        if (canGangWei || canGangPeng){
            eatFlag += this.EatFlags.pao;
        }

        if (pl.jiazhuNum == 1) { // 举手后不能吃 碰
            return eatFlag;
        }

        //碰牌
        if (!pl.isBlockEat && pl.skipPeng.indexOf(newCard) < 0 && this.majiang.canPeng(pl.mjhand, newCard) && this.majiang.remove34Left(pl.mjhand) > 2) {
            eatFlag += this.EatFlags.peng;
        }

        //吃牌  我和下一家能吃， 上家、下家的下家不能吃, 如果这张牌 我或下家已经打出过，就不能吃
        if ((pl.uid == tData.uids[tData.curPlayer] && tData.putType == 1) || pl.uid == tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]) {
            var selfPutList = [].concat(pl.mjput); // 自己弃牌
            if (pl.uid == tData.uids[tData.curPlayer]) {
                selfPutList.pop();
            }

            var preUid = tData.uids[(tData.uids.indexOf(pl.uid) - 1 + tData.maxPlayer) % tData.maxPlayer];
            var prePl = tb.getPlayer(preUid); // 上家弃牌
            var prePutList = [].concat(prePl.mjput);
            if (prePl.uid == tData.uids[tData.curPlayer]) {
                prePutList.pop();
            }

            if (!pl.isBlockEat &&
                pl.skipChi.indexOf(newCard) < 0 &&
                selfPutList.indexOf(newCard) < 0 &&
                prePutList.indexOf(newCard) < 0 &&
                this.majiang.canChiCard(pl.mjhand, newCard, tData.areaSelectMode.isYiwushi)
            ) {
                eatFlag += this.EatFlags.chi;
            }
        }
    }

    return eatFlag;
};

GameCodeXiangXiangPHZ.prototype.getAutoHuUid = function(tb) {
    return null;
    var tData = tb.tData;
    // 0.有胡必胡 1.点炮必胡
    if (tData.gameType == GAME_TYPE.HY_LIU_HU_QIANG) {
        if (tData.putType == 1 && tData.areaSelectMode.bihuType == 1) {
            return null;
        }
    }

    // 0.有胡必胡 1.点炮必胡 2.无必胡
    if (tData.gameType == GAME_TYPE.HY_SHI_HU_KA) {
        if (tData.areaSelectMode.bihuType == 2) {
            return null;
        }

        if (tData.putType == 1 && tData.areaSelectMode.bihuType == 1) {
            return null;
        }
    }

    var autoHuUid;
    for (var i = 0; i < tData.maxPlayer; i++) {
        var uid = tData.uids[(tData.curPlayer + i) % tData.maxPlayer];
        var pl = tb.getPlayer(uid);
        if (pl.eatFlag & this.EatFlags.hu) {
            autoHuUid = pl.uid;
            break;
        }
    }

    return autoHuUid;
};

GameCodeXiangXiangPHZ.prototype.checkAutoHu = function(uid, tb) {
    if (uid) {
        var pl = tb.getPlayer(uid);
        this.mjHuCard(pl, {eatFlag: this.EatFlags.hu}, null, function() {}, tb);
    }
};

//发牌(延迟发牌)
GameCodeXiangXiangPHZ.prototype.schedulerNewCard = function(tb, pWaitTime) {
    this.sendNewCard(tb, true);
};

// 发牌
//首先判断当前玩家能否胡牌
//如果点了王闯或者王钓，则可以直接胡牌
//如果摸到的是王霸牌，则根据eat状态定义状态
//在没有能胡牌的玩家的情况下，如果有玩家能跑，则将其余玩家的状态置为0
GameCodeXiangXiangPHZ.prototype.sendNewCard = function(tb, shouldDelay) {
    // 托管选项  重跑 摸、打的牌没人要自动发牌 延时发牌
    if (shouldDelay && tb.tData.areaSelectMode.trustTime != -1) {
        setTimeout(() => {
            this.sendNewCard(tb);
        }, 1100);
        return;
    }

    var tData = tb.tData;
    var cards = tb.cards;
    logger.debug("======sendNewCard=======")
    if (this.isTableState(TableState.waitCard, tb)) {
        // 发牌前， 清空 所有记录玩家已选择的操作列表  不清空导致有胡会自动跑
        this.cleanObject(tb.AllPlayerEatSelect);

        var totalCard = this.getCardTotalNum();
        if (tData.cardNext < totalCard - tData.huangNum) {
            var pPlayer = this.getPutCardPlayer(tb);
            var newCard = cards[tData.cardNext++];
            tData.lastDrawPlayer = tData.curPlayer;
            tData.putType = 1;
            this.setLastPutCard(newCard, this.PutCardState.moPutCard, pPlayer, tb);
            tData.currCard = newCard; //  显示在桌面的牌
            var hadEatFlag = this.updateAllPlayerEatFlag(newCard, tb.AllPlayerEatFlags, null, tb);
            if (hadEatFlag) {
                this.setTableState(TableState.waitEat, tb);
            }

            if(pPlayer.startTiNum > 0){
                var flag = pPlayer.eatFlag;
                pPlayer.eatFlag = flag - (flag & this.EatFlags.chi);
                tb.AllPlayerEatFlags[pPlayer.uid] = pPlayer.eatFlag;
                tb.AllPlayerRun(function(p){
                    if(p != pPlayer){
                        tb.AllPlayerEatFlags[p.uid] = 0;
                        p.eatFlag = 0;
                    }
                });
            }

            var msg = {
                uid: pPlayer.uid,
                newCard: newCard,
                isCommon: true,
                tData: tData,
                eatFlags: utils.copyPtys(tb.AllPlayerEatFlags),
                isQiHu: pPlayer.isQiHu,
                mjHide: pPlayer.mjHide
            };

            var autoHuUid = this.getAutoHuUid(tb);
            if (autoHuUid && tData.areaSelectMode.trustTime > 0) {
                msg.eatFlags[autoHuUid] = 0;
            }
            // 发牌 后续流程分支(自动提偎跑 自动胡 入手 等待吃 发牌 )
            if (this.isWaitOperate(tb, autoHuUid)) {
                tb.AllPlayerRun(p => {
                    this.checkTrust(tb, p);
                });
            }

            tb.NotifyAll("HZNewCard", msg);
            cloneDataAndPush(tb.mjlog, "HZNewCard", msg); //发牌

            if (tData.isLastDraw) {  // 亮张放入手牌
                this.pickLastCard(tb, pPlayer);
            }else if(!tData.isLastDraw && pPlayer.startTiNum > 0){
                if(pPlayer.eatFlag <= 0){
                    this.pickLastCard(tb, pPlayer);
                    this.setNextPutCardPlayer(null, tb);
                    this.setTableState(TableState.waitCard, tb);
                    this.setPlayerState(pPlayer, TableState.waitCard);
                    pPlayer.startTiNum--;
                }
                // this.pickLastCard(tb, pPlayer);
                // if(pPlayer.startTiNum > 0){
                //     this.setNextPutCardPlayer(null, tb);
                // }

                // if(pPlayer.eatFlag & this.EatFlags.ti || pPlayer.eatFlag & this.EatFlags.pao)
                // {
                //     logger.debug("补张 重跑或提");
                // }
                // else
                //     pPlayer.startTiNum--;
                // this.setTableState(TableState.waitCard, tb);
                // this.setPlayerState(pPlayer, TableState.waitCard);
            } else if (this.isTableState(TableState.waitCard, tb)) {
                this.setNextPutCardPlayer(null, tb);
            }

            if (hadEatFlag) {
                this.checkAutoDoEatSelect(tb.AllPlayerEatFlags, tb);
            }
            this.schedulerNewCard(tb);
        } else {
            this.EndGame(tb, null);
        }
    }
};

//在没有能胡牌的玩家的情况下，如果有玩家能跑，则将其余玩家的状态置为0
GameCodeXiangXiangPHZ.prototype.mjPutCard = function(pl, msg, session, next, tb) {
    var putCard = msg.card;
    //坎牌或者提牌不能打出
    if (this.majiang.cardHandCount(pl.mjhand, putCard) >= 3) {
        return this.updateClientScene(pl, tb);
    }
    logger.debug("======mjPutCard=========");
    if (this.isTableState(TableState.waitPut, tb) && this.isPlayerState(pl, TableState.waitPut, tb) && this.isPutCardPlayer(pl.uid, tb)) {
        var tData = tb.tData;
        var cdIdx = pl.mjhand.indexOf(putCard);
        // 如果手上有这张牌
        if (cdIdx >= 0) {
            if (pl.canNotPutCard.indexOf(putCard) >= 0) { // 不能打的牌
                var canPutCardNum = this.majiang.getCanPutCardNum(pl);
                if (canPutCardNum > 0) {
                    return;
                }
            }

            tb.AllPlayerRun(p => {
                this.clearTrustTimer(tb, p);
            });

            if (pl.limitHuPutCard.indexOf(putCard) >= 0) { // 打后限胡的牌
                pl.limitHuTypeList.push(this.majiang.putCard2LimitHu[putCard]);
            }

            pl.isPutCardOnce = true;

            // 每打掉一张牌清空
            pl.canNotPutCard = [];
            pl.limitHuPutCard = [];

            this.setLastPutCard(putCard, this.PutCardState.eatPutCard, pl, tb);
            this.setLastPutCardPlayerIndex(this.getPutCardPlayerIndex(tb), tb);
            tData.currCard = putCard; //  显示在桌面的牌
            tData.putType = 0;
            tData.lastPutPlayer = tData.curPlayer;
            pl.skipChi.push(putCard);
            pl.mjPutCount++;

            tData.isFirstPut = false; // 当前打出的牌是否第一张标志
            if (tData.isLastDraw) { // 当前牌是否亮张
                tData.isFirstPut = true;
                tData.isLastDraw = false;

                tb.AllPlayerRun(function(p) { // 庄家打第一张牌后 闲家提垅
                    if (p != pl) {
                        this.tiLong(tb, p, true);
                        p.tiLong = true;
                    }
                }.bind(this));
            }

            var ignoreEats = {};
            var hadEatFlag = false;
            ignoreEats[pl.uid] = this.EatFlags.hu; // 玩家不能胡自己 吃碰杠后 打出的牌 // 不要这样处理 而且要么传数组
            hadEatFlag = this.updateAllPlayerEatFlag(putCard, tb.AllPlayerEatFlags, ignoreEats, tb);
            if (hadEatFlag) {
                this.setTableState(TableState.waitEat, tb);
            } else {
                this.setTableState(TableState.waitCard, tb);
                this.setPlayerState(pl, TableState.waitCard);
                this.setNextPutCardPlayer(null, tb);
            }
            var putmsg = {};
            putmsg.card = putCard;
            putmsg.putType = tData.putType;
            putmsg.uid = pl.uid;
            putmsg.eatFlags = utils.copyPtys(tb.AllPlayerEatFlags);
            putmsg.skipPeng = pl.skipPeng;
            putmsg.isQiHu = pl.isQiHu;
            putmsg.putCount = pl.mjPutCount;
            var autoHuUid = this.getAutoHuUid(tb);
            if (autoHuUid) {
                putmsg.eatFlags[autoHuUid] = 0;
            }

            if (this.isWaitOperate(tb, autoHuUid)) {
                tb.AllPlayerRun(p => {
                    this.checkTrust(tb, p);
                });
            }

            tb.NotifyAll("MJPut", putmsg);
            cloneDataAndPush(tb.mjlog, "MJPut", putmsg);

           if (hadEatFlag) {
                this.checkAutoDoEatSelect(tb.AllPlayerEatFlags, tb);
            } else {
                this.schedulerNewCard(tb);
            }

            this.checkAutoHu(autoHuUid, tb);
        } else {
            this.updateClientScene(pl, tb);
        }
    }
};

//过
//如果玩家摸到王霸牌后可以胡，然后选择过，则玩家需要打出一张牌
//判断是否死手
//玩家跑牌之后，可以胡牌，先判断是不是死手，然后判断是否有2个以上的跑
//判断最后一张牌是不是提或者跑，如果不是，则需要改变状态
//如果过胡，则判断是否死手
// 1.过牌后， 发牌给下一家
// 2.如果是 庄家起手可以胡牌时， 选择过后， 还需要打出一张牌
GameCodeXiangXiangPHZ.prototype.mjPassCard = function(pl, msg, session, next, tb) {

    if (this.isTableState(TableState.waitEat, tb) && this.isPlayerState(pl, TableState.waitEat, tb) ) {
        var lputCard = this.getLastPutCardData("value", tb);
        var tData = tb.tData;

        if ((msg.card && lputCard !== msg.card) || (msg.cardNext && msg.cardNext !== tData.cardNext)) {
            return;
        }

        this.clearTrustTimer(tb, pl);
        this.checkCancelTrust(pl, msg, session, next, tb);

        var canDoGuo = false;
        var eatSelectCallBack = function() {
            if (!this.checkAutoDoEatSelect(tb.AllPlayerEatFlags, tb)) {
                this.setTableState(TableState.waitCard, tb)
                this.setNextPutCardPlayer(null, tb);
                this.sendNewCard(tb);
            }
        }.bind(this);

        // 如果 (开局时 && 庄家过胡) 还需要打出一张牌
        if (tData.putType == 1 && tData.isLastDraw && this.isPlayerEatFlag(pl, this.EatFlags.hu)) { // 天胡
            this.setTableState(TableState.waitPut, tb);
            this.setPlayerState(pl, TableState.waitPut);

            this.checkTrust(tb, pl);
        } else if(this.isPlayerEatFlag(pl, this.EatFlags.hu) && this.isPutCardPlayer(pl.uid, tb) && this.getLastPutCardData('state', tb) == this.PutCardState.eatCard ){ // 提偎跑后过胡
            this.setTableState(TableState.waitPut, tb);
            this.setPlayerState(pl, TableState.waitPut);

            this.checkTrust(tb, pl);
        } else {
            // 无必胡 非自摸过胡 
            if (tData.areaSelectMode.bihuType == 2 && this.isPlayerEatFlag(pl, this.EatFlags.hu) 
                && !(tData.putType == 1 && tData.uids[tData.lastDrawPlayer] == pl.uid)
            ) {
                pl.skipHuWinone = this.majiang.canHu(tb, pl, lputCard).maxWinOne;
            }

            if (this.isPlayerEatFlag(pl, this.EatFlags.peng)) {
                pl.skipPeng.push(lputCard);
            }

            this.setPlayerState(pl, TableState.waitCard);
            this.setPlayerEatSelect(pl, this.EatPriority.GuoPai, pl.eatFlag, eatSelectCallBack, tb);
            canDoGuo = true;
        }

        this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);

        pl.notify("MJPass", {
            tState: tb.tData.tState,
            mjState: pl.mjState,
            isQiHu: pl.isQiHu
        });
        cloneDataAndPush(tb.mjlog, "MJPass", {mjState: pl.mjState,}, {uid: pl.uid});
        
        if(canDoGuo)
            this.doPlayerEatSelect(tb);


    } else if ((this.isTableState(TableState.roundFinish, tb) && this.isPlayerState(pl, TableState.roundFinish, tb)) ||
        (this.isTableState(TableState.waitReady, tb) && this.isPlayerState(pl, TableState.waitReady, tb))
    ) {
        this.setPlayerState(pl, TableState.isReady)
        tb.NotifyAll('onlinePlayer', {
            uid: pl.uid,
            onLine: true,
            mjState: pl.mjState
        });
        pl.eatFlag = this.EatFlags.wu;

        var tData = tb.tData;
        if (tb.PlayerCount() == tData.maxPlayer && tb.AllPlayerCheck(function(p) {return p.mjState == TableState.isReady;})) {
            if (tData.gameType ==  GAME_TYPE.HY_SHI_HU_KA && tData.areaSelectMode.isJiaChui) {
                if (tData.tState == TableState.roundFinish) {
                    this.countZhuang(tData, tb);
                }
                
                tData.tState = TableState.waitJiazhu;
                tb.AllPlayerRun(function(p) {
                    p.jiachuiNum = -1;
                });
                tb.NotifyAll("waitJiazhu", {});
            } else {
                if (tData.tState == TableState.roundFinish) {
                    this.countZhuang(tData, tb);
                }

                if(tData.areaSelectMode.isManualCutCard){ // 切牌
                    this.startShuffleCards(tb);
                }else{
                    tb.runStartGame();
                } 
 
            }
        }
    }
};

//吃
//如果选择了吃，就相当于放弃了其他操作
//其他玩家不能胡、跑、碰;
//  最近打出在桌面上的牌
GameCodeXiangXiangPHZ.prototype.mjChiCard = function(pl, msg, session, next, tb) {
    if (!(this.isTableState(TableState.waitEat, tb) && this.isPlayerState(pl, TableState.waitEat, tb) && this.isPlayerEatFlag(pl, this.EatFlags.chi))) {
        return;
    }

    var eatCard = this.getLastPutCardData("value", tb);
    var tData = tb.tData;
    // 是对当前牌的吃请求验证
    if ((msg.card && msg.card !== eatCard) || (msg.cardNext && msg.cardNext !== tData.cardNext)) {
        return;
    }

    if (msg.eatCards.indexOf(eatCard) < 0) {
        var info = {
            tableid: tData.tableid,
            roundNum: tData.roundNum,
            uid: pl.uid,
            card: msg.card,
            eatCards: msg.eatCards,
            cardNetx: msg.cardNext,
            card2: eatCard,
            cardNetx2: tData.cardNext
        };
        logger.error("eatError@ ", JSON.stringify(info));
        return;
    }
    
    // 手牌包含吃牌 验证
    var eatCards = msg.eatCards;
    var biCards = msg.biCards;
    var eatDict = {};
    for (var i = 0; i < eatCards.length; i++) {
        var card = eatCards[i];
        eatDict[card] = eatDict[card] ? eatDict[card] + 1 : 1;
    }
    eatDict[eatCard]--;
    if (biCards) {
        for (var i = 0; i < biCards.length; i++) {
            var biRow = biCards[i];
            for (var j = 0; j < biRow.length; j++) {
                var card = biRow[j];
                eatDict[card] = eatDict[card] ? eatDict[card] + 1 : 1;
            }
        }
    }

    var handDict = {};
    for (var i = 0; i < pl.mjhand.length; i++) {
        var card = pl.mjhand[i];
        handDict[card] = handDict[card] ? handDict[card] + 1 : 1;
    }

    if (handDict[eatCard] && eatDict[eatCard] != handDict[eatCard]) {
        return;
    }

    for (var card in eatDict) {
        if (Number(card) == eatCard) {
            continue;
        }

        if (!handDict[card] || handDict[card] < eatDict[card] || handDict[card] >= 3) {
            return;
        }
    }

    this.clearTrustTimer(tb, pl);
    this.checkCancelTrust(pl, msg, session, next, tb);

    // 选择吃计入过碰
    if (this.isPlayerEatFlag(pl, this.EatFlags.peng)) {
        pl.skipPeng.push(eatCard);
    }

    // 能胡选吃 计入过胡
    if (tData.areaSelectMode.bihuType == 2  && this.isPlayerEatFlag(pl, this.EatFlags.hu) 
        && !(tData.putType == 1 && tData.uids[tData.lastDrawPlayer] == pl.uid)
    ) {
        pl.skipHuWinone = this.majiang.canHu(tb, pl, eatCard).maxWinOne;
    }

    var eatSelectCallBack = function() {
        this.doChiCard(pl, msg.eatCards, msg.biCards, tb);
    }.bind(this);

    this.setPlayerEatSelect(pl, this.EatPriority.ChiPai, pl.eatFlag, eatSelectCallBack, tb);
    this.doPlayerEatSelect(tb);

};

/**
 * 执行吃牌操作
 * @param  {object} pl 玩家数据对象
 * @param  {array} pEatCards 吃的牌型
 * @param  {array[array]} pBiCards 下火的牌 二维数据
 */
GameCodeXiangXiangPHZ.prototype.doChiCard = function(pl, pEatCards, pBiCards, tb) {
    tb.AllPlayerRun(p => {
        if (p != pl) {
            this.clearTrustTimer(tb, p);
        }
    });

    var tData = tb.tData;
    var eatCard = this.getLastPutCardData("value", tb);

    // 删除用来吃牌的手牌
    var eatCardsArr = [];
    eatCardsArr = eatCardsArr.concat(pEatCards);
    eatCardsArr.splice(eatCardsArr.indexOf(eatCard), 1); // 吃的牌不属于手牌，不用删除
    for (var i in pBiCards) eatCardsArr = eatCardsArr.concat(pBiCards[i]);
    this.deleteCard(pl, 'mjhand', eatCardsArr);
    
    // 删除被吃玩家打出的牌
    var putCardPl = this.getPutCardPlayer(tb);
    putCardPl.mjput.length = putCardPl.mjput.length - 1;

    // 吃上家摸的牌 计入上家过吃
    if (tData.putType == 1 && pl.uid == tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]) {
        putCardPl.skipChi.push(eatCard);
    }

    // 记录吃的牌型， 和吃的那张牌
    var chiscore = this.majiang.getYiJuHuaScore(pEatCards);
    var eatAndBiCards = {};
    pEatCards.splice(pEatCards.indexOf(eatCard), 1);
    pEatCards.sort(function(a, b) {
        if (a % 20 == b % 20) {
            return b - a;
        }

        return a - b;
    });
    pEatCards.push(eatCard);
    eatAndBiCards.eatCards = pEatCards;
    if (pBiCards) {
        var biCards = [];
        for (var i = 0; i < pBiCards.length; i++) {
            var biRow = pBiCards[i];
            biRow.splice(biRow.indexOf(eatCard), 1);
            biRow.sort(function(a, b) {
                if (a % 20 == b % 20) {
                    return b - a;
                }

                return a - b;
            });
            biRow.push(eatCard);
            biCards.push(biRow);
        }
        eatAndBiCards.biCards = biCards;
    }
    pl.mjchi.push(eatAndBiCards);
    pl.mjchiCard.push(eatCard);
    pl.mjsort.push({name: "mjchi", pos: pl.mjchi.length - 1, score: chiscore});

    tData.currCard = -1; //  显示在桌面的牌
    
    //吃碰杠
    var cpginfo = {
        uid: pl.uid,
        mjsort: pl.mjsort,
        isQiHu: pl.isQiHu
    };
    var chiMsg = {
        mjchi: pl.mjchi,
        mjchiCard: pl.mjchiCard,
        tData: tb.tData,
        from: this.getPutCardPlayerIndex(tb),
        cpginfo: cpginfo,
        canNotPutCard: pl.canNotPutCard,
        limitHuPutCard: pl.limitHuPutCard
    };

    this.setTableState(TableState.waitPut, tb);
    this.setPlayerState(pl, TableState.waitPut);
    this.setLastPutCardState(this.PutCardState.eatCard, tb);
    this.setNextPutCardPlayer(pl.uid, tb);
    var buZhang = this.cpgBuPaiOperator(tb, pl);

    // 吃 后续流程(等待出牌)
    this.checkTrust(tb, pl);

    tb.NotifyAll('HZChiCard', chiMsg);
    cloneDataAndPush(tb.mjlog, "HZChiCard", chiMsg);
    this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);
    if(buZhang){
        this.setNextPutCardPlayer(null, tb);
        this.schedulerNewCard(tb);
    }
};

// 碰
//此处必须保证没有其他玩家想胡、提、偎、跑牌
GameCodeXiangXiangPHZ.prototype.mjPengCard = function(pl, msg, session, next, tb) {

    if (this.isTableState(TableState.waitEat, tb) && this.isPlayerState(pl, TableState.waitEat, tb) && this.isPlayerEatFlag(pl, this.EatFlags.peng) ) {
        this.clearTrustTimer(tb, pl);
        this.checkCancelTrust(pl, msg, session, next, tb);

        var putCard = this.getLastPutCardData("value", tb);
        if (tb.tData.areaSelectMode.bihuType == 2 && this.isPlayerEatFlag(pl, this.EatFlags.hu)) {
            pl.skipHuWinone = this.majiang.canHu(tb, pl, putCard).maxWinOne;
        }

        var eatSelectCallBack = function() {
            this.doPengCard(pl, tb);
        }.bind(this);

        this.setPlayerEatSelect(pl, this.EatPriority.PengPai, pl.eatFlag, eatSelectCallBack, tb);
        this.doPlayerEatSelect(tb);

    }

};

GameCodeXiangXiangPHZ.prototype.doPengCard = function(pl, tb) {
    tb.AllPlayerRun(p => {
        if (p != pl) {
            this.clearTrustTimer(tb, p);
        }
    });

    var putCard = this.getLastPutCardData("value", tb);;
    var matchnum = this.majiang.getCardCount(pl.mjhand, putCard)
    if (matchnum == 2) {
        var tData = tb.tData;
        // 删除pl碰了的手牌
        this.deleteCard(pl, 'mjhand', [putCard, putCard]);
        pl.mjpeng.push(putCard);
        // 删除被碰玩家打出的牌
        var putCardPl = this.getPutCardPlayer(tb);
        putCardPl.mjput.length = putCardPl.mjput.length - 1;
        // 碰记录到pl桌面上的门子
        var pScore = this.majiang.huXiScore('peng', putCard);
        pl.mjsort.push({name: "mjpeng", pos: pl.mjpeng.length - 1, score: pScore});

        tData.currCard = -1; //  显示在桌面的牌
        var from = this.getPutCardPlayerIndex(tb);
        this.setLastPutCardState(this.PutCardState.eatCard, tb);
        this.setNextPutCardPlayer(pl.uid, tb);
        this.setTableState(TableState.waitPut, tb);
        this.setPlayerState(pl, TableState.waitPut);

        var buZhang = this.cpgBuPaiOperator(tb, pl);

        //吃碰杠
        var cpginfo = {
            id: pl.uid,
            pengchigang: pl.pengchigang,
            mjsort: pl.mjsort,
            isQiHu: pl.isQiHu
        };

        var msgpeng = {
            tData: tData,
            from: from,
            cpginfo: cpginfo
        };

        // 碰 后续流程(等待出牌)
        this.checkTrust(tb, pl);

        tb.NotifyAll('MJPeng', msgpeng);

        cloneDataAndPush(tb.mjlog, 'MJPeng', {
            tData: tData,
            from: from,
            cpginfo: cpginfo
        });
        this.setPlayerProperty(pl.uid, 'eatFalg', this.EatFlags.wu, tb);
        if(buZhang){
            this.setNextPutCardPlayer(null, tb);
            this.schedulerNewCard(tb);
        }
    }
};

//延迟0.8秒之后发送偎牌命令
GameCodeXiangXiangPHZ.prototype.hzWeiCard = function(pl, msg, session, next, tb) {
    if (this.isTableState(TableState.waitEat, tb) && this.isPlayerState(pl, TableState.waitEat, tb) && this.isPlayerEatFlag(pl, this.EatFlags.wei) ) {
        var eatSelectCallBack = function() {
            this.doWeiCard(pl, tb);
        }.bind(this);

        this.setPlayerEatSelect(pl, this.EatPriority.WeiPai, pl.eatFlag, eatSelectCallBack, tb);
        this.doPlayerEatSelect(tb);
        // 执行偎牌后， 重置操作， 重连的时候就不会显示操作按钮
        this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);
    }
};

// 偎牌
// 不能胡牌的情况下进行弃胡判断
GameCodeXiangXiangPHZ.prototype.doWeiCard = function(pl, tb, shouldDelay) {
    if (shouldDelay && tb.tData.areaSelectMode.trustTime != -1) {
        setTimeout(() => {
            this.doWeiCard(pl, tb);
        }, 1100);
        return;
    }

    var putCard = this.getLastPutCardData('value', tb);
    var handCount = this.majiang.cardHandCount(pl.mjhand, putCard);
    if (handCount != 2) return;

    this.deleteCard(pl, 'mjhand', [putCard, putCard]);
    pl.mjwei.push(putCard);

    // 删除玩家mjput
    pl.mjput.length = pl.mjput.length - 1;

    var weiScore = this.majiang.huXiScore('weiPai', putCard);
    var pluid = pl.uid;
    pl.mjsort.push({
        name: "mjwei",
        pos: pl.mjwei.length - 1,
        score: weiScore
    });

    tb.tData.currCard = -1;
    this.setLastPutCardState(this.PutCardState.eatCard, tb);
    this.setLastPutCardPlayerIndex(this.getPutCardPlayerIndex(tb), tb);
    this.setNextPutCardPlayer(pluid, tb);
    this.setTableState(TableState.waitPut, tb);
    this.setPlayerState(pl, TableState.waitPut);

    // 判断偎后  是否能胡 是则更新状态
    var mEatFlags = {};
    var eatFlag = this.getEatFlag(pl, 0, tb);
    if (eatFlag > 0) {
        this.setTableState(TableState.waitEat, tb);
        this.setPlayerState(pl, TableState.waitEat);
        this.setPlayerProperty(pluid, 'eatFlag', eatFlag, tb);
        mEatFlags[pl.uid] = eatFlag;
    }

    var autoHu = false;
    var sendNew = false;
    var buZhang = false;
    if (this.majiang.remove34Left(pl.mjhand) == 0) { // 死手
        if (eatFlag > 0) { // 能胡自动胡
            autoHu = true;
            mEatFlags[pl.uid] = this.EatFlags.wu; // 发给前端eatFlag置为0
        } else { // 不能胡给下家发牌
            sendNew = true;
            pl.isWeiDead = true; // 偎牌后死手
            this.setTableState(TableState.waitCard, tb);
            this.setPlayerState(pl, TableState.waitCard);
        }
    }else{
        buZhang = this.cpgBuPaiOperator(tb, pl);
    }

    var sendMsg = {
        newCard: putCard,
        mjsort: pl.mjsort,
        tData: utils.copyPtys(tb.tData),
        eatFlags: mEatFlags,
        skipPeng: pl.skipPeng,
        isQiHu: pl.isQiHu
    };
    var autoHuUid = this.getAutoHuUid(tb);
    if (autoHuUid) {
        sendMsg.eatFlags[autoHuUid] = 0;
    }

    // 偎 后续流程分支(自动胡 等待胡 自动发牌(死手) 等待出牌)
    tb.AllPlayerRun(p => {
        this.clearTrustTimer(tb, p);
    });
    if (!autoHu && this.isWaitOperate(tb, autoHuUid)) {
        this.checkTrust(tb, pl);
    }

    tb.NotifyAll("HZWeiCard", sendMsg);
    cloneDataAndPush(tb.mjlog, "HZWeiCard", sendMsg);

    if (sendNew) {
        this.setNextPutCardPlayer(null, tb);
        this.schedulerNewCard(tb);
    } else if (autoHu) {
        this.doHuCard(pl, this.EatFlags.hu, tb);
    } else if(buZhang){
        this.setNextPutCardPlayer(null, tb);
        this.schedulerNewCard(tb);
    } else {
        this.checkAutoHu(autoHuUid, tb);
    }
};

//获得桌面上的牌的操作顺序
function getIndexFromSort(mjsort, name, index) {
    for (var i = 0; i < mjsort.length; i++) {
        var sort = mjsort[i];
        if (sort.name == name && sort.pos == index) {
            return i;
        }
    }
    return mjsort.length;
}

//刷新mjsort中的index(主要针对提、跑)
function updateSortIndex(mjsort, name, beginIndex) {
    for (var i = 0; i < mjsort.length; i++) {
        var sort = mjsort[i];
        if (sort.name == name) {
            if (sort.pos >= beginIndex) {
                sort.pos -= 1;
            }
        }
    }
}

GameCodeXiangXiangPHZ.prototype.mjGangCard = function(pl, msg, session, next, tb) {
};

/**
 * 执行杠牌(提牌 或 跑牌) = (暗杠 或 明杠)
 * @param  {object} pl 玩家数据对象
 * @param  {int} pType 刚的类型 提牌:1  跑牌:2
 */
GameCodeXiangXiangPHZ.prototype.doGangCard = function(pl, pType, tb, tiCard, shouldDelay) {
    if (shouldDelay && tb.tData.areaSelectMode.trustTime != -1) {
        setTimeout(() => {
            this.doGangCard(pl, pType, tb, tiCard, false);
        }, 1100);
        return;
    }

    var tb = tb;
    var tData = tb.tData;
    var sendMsg = {};
    var eatFlags = {};
    var eatFlag = 0;
    var putCard = this.getLastPutCardData("value", tb);
    var isGangHand = false;
    if (tiCard && pType == 1) { // 手牌4张提垅
        putCard = tiCard;
        if (this.majiang.cardHandCount(pl.mjhand, putCard) < 4) {
            return;
        }

        pl.mjgang1.push(putCard);
        pl.roomStatistics[3]++;
        var huxi = this.majiang.huXiScore('tiPai', putCard);
        var cardSort = {name: "mjgang1",   pos: pl.mjgang1.length - 1, score: huxi};
        this.deleteCard(pl, 'mjhand', [putCard, putCard, putCard, putCard]);
        pl.mjsort.push(cardSort);
        isGangHand = true;
    } else if (this.isPutCardPlayer(pl.uid, tb) && pType == 1) {
        pl.mjgang1.push(putCard);
        pl.roomStatistics[3]++;
        this.deleteCard(pl, 'mjput', [putCard]);
        var huxi = this.majiang.huXiScore('tiPai', putCard);
        var cardSort = {name: "mjgang1",   pos: pl.mjgang1.length - 1, score: huxi};
        if (pl.mjhand.indexOf(putCard) >= 0) {
            this.deleteCard(pl, 'mjhand', [putCard, putCard, putCard]);
            pl.mjsort.push(cardSort);
        } else {
            var weiIndex = pl.mjwei.indexOf(putCard);
            pl.mjwei.splice(weiIndex, 1);
            var sortIndex = getIndexFromSort(pl.mjsort, "mjwei", weiIndex);
            pl.mjsort.splice(sortIndex, 1, cardSort);
            updateSortIndex(pl.mjsort, "mjwei", weiIndex);
        }
    } else if (pType == 2) {
        var putCardPl = this.getPutCardPlayer(tb);
        var isMoPutCard = tb.tData.putType == 1;
        var pos = this.majiang.paoPos(pl, putCard, isMoPutCard);
        if (pos == 0) return;

        this.deleteCard(putCardPl, 'mjput', [putCard]);
        pl.mjgang0.push(putCard);
        pl.roomStatistics[4]++;
        var huxi = this.majiang.huXiScore('paoPai', putCard);
        var cardSort = {name: "mjgang0", pos: pl.mjgang0.length - 1, score: huxi};

        if (pos == 1) { //手牌跑
            this.deleteCard(pl, 'mjhand', [putCard, putCard, putCard]);
            pl.mjsort.push(cardSort);

        } else if (pos == 2) { //偎牌跑
            var weiIndex = pl.mjwei.indexOf(putCard);
            pl.mjwei.splice(weiIndex,1);
            var index = getIndexFromSort(pl.mjsort,"mjwei",weiIndex);
            pl.mjsort.splice(index,1,cardSort);
            updateSortIndex(pl.mjsort,"mjwei",weiIndex);

            if (tData.putType == 0 && pl.mjHide.indexOf(putCard) < 0) { // 放跑不能吃碰
                var putPl = tb.getPlayer(tData.uids[tData.lastPutPlayer]);
                putPl.isBlockEat = true;
            }
        } else if (pos == 3) { //碰牌跑
            var pengIndex = pl.mjpeng.indexOf(putCard);
            pl.mjpeng.splice(pengIndex, 1);
            var index = getIndexFromSort(pl.mjsort, "mjpeng", pengIndex);
            pl.mjsort.splice(index, 1, cardSort);
            updateSortIndex(pl.mjsort, "mjpeng", pengIndex);
        }
        
        sendMsg.pos = pos;
        sendMsg.from = this.getPutCardPlayerIndex(tb);
    }

    if (!isGangHand) {
        tData.currCard = -1;   //  显示在桌面的牌
        this.setLastPutCardState(this.PutCardState.eatCard, tb);
        this.setLastPutCardPlayerIndex( this.getPutCardPlayerIndex(tb), tb);
        this.setNextPutCardPlayer(pl.uid, tb);// 跑牌 提牌后 需要出牌

        if (pl.autoHu) { // 跑胡走跑 后调胡
            this.doHuCard(pl, this.EatFlags.hu, tb);
            return;
        }

        eatFlag = this.getEatFlag(pl, 0, tb);
        if (eatFlag & this.EatFlags.hu) {
            this.setPlayerProperty(pl.uid, 'eatFlag', eatFlag, tb);
            this.setTableState(TableState.waitEat, tb);
            this.setPlayerState(pl, TableState.waitEat);
            eatFlags[pl.uid] = eatFlag;
            
        } else {
            var paoTiCount = pl.mjgang0.length + pl.mjgang1.length;
            var dirt = {};
            for (var t = 0; t < pl.mjhand.length; t++) {
                var tmpCard = pl.mjhand[t];
                if (!dirt[tmpCard]) {
                    dirt[tmpCard] = 1;
                } else {
                    dirt[tmpCard]++;
                }
                if (dirt[tmpCard] == 4) {
                    paoTiCount++;
                }
            }

            if (paoTiCount >= 2 || pl.isWeiDead) { // 之前偎后死手 提或跑后给下家发牌
           
                this.setTableState(TableState.waitCard, tb)
                this.setPlayerState(pl, TableState.waitCard)
                if (!utils.isContains(['shaoyang', 'shaoyang-test', 'dev-b', 'dev-c'], env)) {
                    this.setNextPutCardPlayer(null, tb); // 重跑不用打牌 发牌给下一家
                }
            } else {
                this.setTableState(TableState.waitPut, tb);
                this.setPlayerState(pl, TableState.waitPut);
            }
        }
    }

    var cpgInfo = {
        uid : pl.uid,
        mjsort : pl.mjsort,
        isQiHu : pl.isQiHu,
    };

    sendMsg.isGangHand = isGangHand;
    sendMsg.newCard = putCard;
    sendMsg.type = pType;
    sendMsg.eatFlags = eatFlags;
    sendMsg.tData = tData;
    sendMsg.cpginfo = cpgInfo;
    var autoHuUid = this.getAutoHuUid(tb);
    if (autoHuUid) {
        sendMsg.eatFlags[autoHuUid] = 0;
    }

    // 提跑 后续流程分支(自动胡 等待胡 等待出牌 发牌)
    if (!pl.autoHu && this.isWaitOperate(tb, autoHuUid)) {
        this.checkTrust(tb, pl);
    }

    tb.NotifyAll("HZGangCard", sendMsg);
    cloneDataAndPush(tb.mjlog, "HZGangCard", sendMsg);

    if (!isGangHand) { // todo 检查isGangHand时为什么tData.tState 是waitCard
        if (utils.isContains(['shaoyang', 'shaoyang-test', 'dev-b', 'dev-c'], env)) {
            var flag = 0;
            flag = this.getEatFlag(pl, 0, tb);
            if(!(flag & this.EatFlags.hu)) {
                var paoTiCount = pl.mjgang0.length + pl.mjgang1.length;
                var dirt = {};
                for (var t = 0; t < pl.mjhand.length; t++) {
                    var tmpCard = pl.mjhand[t];
                    if (!dirt[tmpCard]) {
                        dirt[tmpCard] = 1;
                    } else {
                        dirt[tmpCard]++;
                    }
                    if (dirt[tmpCard] == 4) {
                        paoTiCount++;
                    }
                }

                if (paoTiCount >= 2 || pl.isWeiDead) {
                    this.setNextPutCardPlayer(null, tb); // 重跑不用打牌 发牌给下一家
                }
            }
        }

        this.schedulerNewCard(tb);
    }
};

// 胡
GameCodeXiangXiangPHZ.prototype.mjHuCard = function(pl, msg, session, next, tb) {
    if (this.isTableState(TableState.waitEat, tb) && this.isPlayerState(pl, TableState.waitEat, tb) && this.isPlayerEatFlag(pl, this.EatFlags.hu) ){
        this.clearTrustTimer(tb, pl);
        this.checkCancelTrust(pl, msg, session, next, tb);

        var eatFlag = pl.eatFlag; //记录 跑胡判断用
        var eatSelectCallBack = function() {
            if ((eatFlag&this.EatFlags.pao) > 0) {
                var huMatrix =  this.majiang.canHu(tb, pl, this.getLastPutCardData("value", tb)).huMatrix[0];
                if (huMatrix[0].length == 4 && huMatrix[0][0] == this.getLastPutCardData("value", tb)) {
                    pl.autoHu = true;
                    this.doGangCard(pl, 2, tb);
                    return;
                }
            }
            this.doHuCard(pl, msg.eatFlag, tb);
        }.bind(this);

        this.setPlayerEatSelect(pl, this.EatPriority.HuPai, pl.eatFlag, eatSelectCallBack, tb);
        this.doPlayerEatSelect(tb);
        
        this.setPlayerProperty(pl.uid, 'eatFlag', this.EatFlags.wu, tb);
    }
};

GameCodeXiangXiangPHZ.prototype.doHuCard = function(pl, pEatFlag, tb){
    // 先处理一下托管的逻辑
    tb.AllPlayerRun(p => {
        if (p != pl) {
            this.clearTrustTimer(tb, p);
        }
    });

    var tData = tb.tData;
    var cardState = this.getLastPutCardData('state', tb);
    var cardValue = this.getLastPutCardData("value", tb);

    if (tData.isLastDraw && pl.uid == tData.uids[tData.zhuang]) { // 庄家天胡 
        pl.isHuByHand = true;
    } else if (cardState == this.PutCardState.eatCard) { // 提偎跑后胡
        pl.isHuByHand = true;
    } else {  // 胡展示的一张牌
        pl.mjhand.push(cardValue);
    }

    pl.roomStatistics[0]++;
    if (tData.putType == 1 && tData.uids[tData.lastDrawPlayer] == pl.uid) {
        pl.winType = WinType.pickNormal; // 自摸
        pl.roomStatistics[1]++;
        pl.zimoTotal += 1; //结算自摸胡加1
    }
    else if(tData.putType == 1 && tData.uids[tData.lastDrawPlayer] != pl.uid) {
        pl.winType = WinType.pinghu; // 平摸
        pl.roomStatistics[2]++;
        pl.pinghuTotal += 1; //结算平摸胡加1
    }

    var huMsg = {
        uid: pl.uid,
        eatFlag: pEatFlag,
        huWord: ""
    };
    tb.NotifyAll("MJHu", huMsg);
    cloneDataAndPush(tb.mjlog, "MJHu", huMsg);

    this.EndGame(tb, pl);
};

GameCodeXiangXiangPHZ.prototype.huScore = function(tb, pl) {
    //算分
    var tData = tb.tData;
    tData.winner = this.getPlayerIndex(pl, tb);

    var huInfo;
    if (pl.isHuByHand) { // 提 偎 跑后胡 天胡
        logger.debug("huScore pl mjhand:" + pl.mjhand);
        huInfo = this.majiang.getHuInfo(tb, pl, null);
    } else {
        var card = pl.mjhand.pop();
        huInfo = this.majiang.getHuInfo(tb, pl, card);
        pl.mjhand.push(card);
    }

    pl.hzdesc = huInfo.hzdesc;
    var totalHuxi = pl.hzdesc.huXi = huInfo.totalHuxi; // 胡息
    pl.hzdesc.totalFan = huInfo.rate; // 番
    pl.handSort = huInfo.handSort;

    var huxinum = tData.minHuxi - 3;
    var score = tData.areaSelectMode.jifenType == 1 ? (Math.floor((totalHuxi - huxinum)/3) * huInfo.rate) : (Math.floor((totalHuxi * huInfo.rate - huxinum)/3));
    pl.hzdesc.totalTun = score;
    pl.winone = (tData.maxPlayer-1) * score;
    tb.AllPlayerRun(function (p) {
        if (p != pl) {
            p.winone -= score;
        }  
        p.winone *= tb.tData.areaSelectMode.jieSuanDiFen;
    }); 
};

GameCodeXiangXiangPHZ.prototype.EndGame = function(tb, pl, byEndRoom) {
    var tData = tb.tData;
    tb.AllPlayerRun(function(p) {
        p.mjState = TableState.roundFinish;
    });

    if (byEndRoom) {
        tb.showDissmissDesc();
    }

    // 算分
    if (pl) {
        this.huScore(tb, pl);
    } else { //流局
        tData.winner = -1;
    }

    //记录玩家每一局的分数
    tb.AllPlayerRun(function(p) {
        p.tableMsg.push(p.winone);
    });

    tData.tState = TableState.roundFinish;
    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }

    tb.AllPlayerRun(function(p) {
        p.winone = revise(p.winone);
    });
    
    tb.AllPlayerRun(function(p) {
        p.winall += p.winone;
        p.winall = revise(p.winall);
    });

    tb.perRoundEndDo();
    tData.roundNum--;
    // tData.cards = tb.cards;
    
    if (byEndRoom || tData.roundNum == 0) {    //大结算翻倍
        this.checkWinAllFanBei(tb);
    }

    var roundEnd = {
        players: tb.collectPlayer('mjhand', 'mjdesc', 'winone', 'winall', 'winType', 'baseWin', "mjwei", "mjsort", "handSort", "hzdesc",
            'zimoTotal', 'dianpaoTotal', 'minggangTotal', 'angangTotal', 'mjpeng', 'mjgang0', 'mjflower', 'info', "tableMsg", "longCard", 
            "isHuByHand" ,'lastOffLineTime', 'roomStatistics','isNeedFanBei'
        ),
        tData: tData,
        cards: tb.cards,
        hunCard: tData.hunCard,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };

    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd); //一局结束
    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb); //结束
    if (playInfo) roundEnd.playInfo = playInfo;
    tb.NotifyAll("roundEnd", roundEnd);
};

GameCodeXiangXiangPHZ.prototype.EndRoom = function(tb, msg) {
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        this.checkWinAllFanBei(tb);
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function(p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({userId: p.uid, lastGameTime: p.info.lastGameTime});
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if(!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('winall', 'isNeedFanBei', 'lastOffLineTime');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeXiangXiangPHZ.prototype.checkWinAllFanBei = function(tb) {

    if(tb.tData.areaSelectMode.fanBei == 1)
    {
        var max = -1;
        tb.AllPlayerRun(function(p) {
            max = p.winall > max? p.winall:max;
        });
        if(max < tb.tData.areaSelectMode.fanBeiScore)
        {
            tb.AllPlayerRun(function(p) {
                if (!p.isNeedFanBei)
                {
                    p.winall *=2;
                    p.isNeedFanBei = true;                
                }
            });
        }
    }
};

GameCodeXiangXiangPHZ.prototype.DestroyTable = function(tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
};

GameCodeXiangXiangPHZ.prototype.countZhuang = function(tData, tb) {

    var zhuangUid = tData.uids[tData.zhuang % tData.maxPlayer];
    var zp = tb.getPlayer(zhuangUid);
    if (tData.zhuang == -1) {
        //第一局
        tData.curPlayer = tData.uids.indexOf(tData.uidsQueue[0]);
        if(tData.areaSelectMode.hasOwnProperty("zuoZhuang")){
            if(tData.areaSelectMode.zuoZhuang == 1 && tData.uids.indexOf(tData.owner) >= 0){
                tData.curPlayer = tData.uids.indexOf(tData.owner);
            }else if(tData.areaSelectMode.zuoZhuang == 0){
                tData.curPlayer = Math.floor(Math.random() * tData.maxPlayer) % tData.maxPlayer;
            }
        }
    } else if (tData.winner != -1) {
        //谁胡牌谁的庄
        tData.curPlayer = tData.winner;
    } else {
        //荒庄 下一家
        tData.curPlayer = (tData.zhuang + 1) % tData.maxPlayer;
    }
    tData.zhuang = tData.curPlayer;
};

GameCodeXiangXiangPHZ.prototype.cpgBuPaiOperator = function (tb, pl) {
    //閑家吃碰偎后補牌操作
    if(pl.startTiNum > 0){
        var state = pl.startTiNum > 0 ? TableState.waitCard : tb.tData.tState;
        pl.startTiNum--;
        this.setTableState(state, tb);
        this.setPlayerState(pl, state);
        if(pl.startTiNum >= 0){
            return true;
        }
        return false;
    }
    return false;
};

/// ========================以下都是托管的接口=======================

GameCodeXiangXiangPHZ.prototype.isWaitOperate = function(tb, autoHuUid) {

    var tData = tb.tData;

    if (tData.tState == TableState.waitCard) {
        return false;
    }

    if (tData.tState == TableState.waitPut) {
        return true;
    }

    if (autoHuUid) {
        return false;
    }

    var maxFlag = 0;
    tb.AllPlayerRun(function(pl) {
        var topFlag = (tb.AllPlayerEatFlags[pl.uid] & 16) || (tb.AllPlayerEatFlags[pl.uid] & 8) || (tb.AllPlayerEatFlags[pl.uid] & 32) || (tb.AllPlayerEatFlags[pl.uid] & 4);
        maxFlag = Math.max(maxFlag, topFlag);
    });

    if (maxFlag == 16 || maxFlag == 8 || maxFlag == 4) { // 自动提 偎 跑
        return false;
    }

    return true;
};

GameCodeXiangXiangPHZ.prototype.doTrustAction = function(tb, pl) {

    logger.debug("fangpaofa", "do trust action");
    // 先清理一下老的定时器 防止引发问题
    if (pl.trustTimer_bp2) {
        clearTimeout(pl.trustTimer_bp2);
    }
    pl.trustTimer_bp2 = setTimeout(() => {

        pl.trustTimer_bp2 = null;

        if (pl.mjState == TableState.waitPut) {
            var dict = {};
            for (var i = 0; i < pl.mjhand.length; i++) {
                var c = pl.mjhand[i];
                dict[c] = dict[c] ? dict[c] + 1 : 1;
            }

            var list = [];
            for (var c in dict) {
                if (dict[c] <= 2) {
                    list.push(Number(c));
                }
            }

            if (list.length == 0) { // 手牌异常容错
                return;
            }

            var card = list[Math.floor(Math.random() * list.length)];
            this.mjPutCard(pl, {card: card, byTrust: true}, null, function() {}, tb);
        }
        else if (pl.mjState == TableState.waitEat) {

            if (pl.eatFlag & 32) {
                this.mjHuCard(pl, {byTrust: true}, null, function() {}, tb);
            }
            else {
                this.mjPassCard(pl, {byTrust: true}, null, function() {}, tb);
            }
        }
    }, 1100);
};

GameCodeXiangXiangPHZ.prototype.doTrustCountdown = function(tb, pl) {

    logger.debug("fangpaofa", "do trust count down",tb.tData.trustEnd);

    var tData = tb.tData;
    if (tData.trustEnd == -1) {
        tData.trustEnd = Date.now() + tData.areaSelectMode.trustTime * 1000;
        tb.NotifyAll("trustTime", {trustEnd: tData.trustEnd});
    }

    pl.trustEnd = tData.trustEnd;

    // 先清除老的定时器
    if (pl.trustTimer_bp) {
        clearTimeout(pl.trustTimer_bp);
    }
    pl.trustTimer_bp = setTimeout(() => {

        pl.trustTimer_bp = null;
        pl.trust = true;

        tb.NotifyAll("beTrust", {uid: pl.uid});
        cloneDataAndPush(tb.mjlog, 'beTrust', {uid: pl.uid});
        tData.trustEnd = -1;
        pl.trustEnd = -1;
        this.doTrustAction(tb, pl);
    }, tData.areaSelectMode.trustTime * 1000);
};

GameCodeXiangXiangPHZ.prototype.checkTrust = function(tb, pl) {
    if (!(tb.tData.areaSelectMode.trustTime > 0)) {
        return;
    }
    if (pl.mjState == TableState.waitCard || pl.mjState == TableState.roundFinish) {
        return;
    }

    pl.trust ? this.doTrustAction(tb, pl) : this.doTrustCountdown(tb, pl);
};

GameCodeXiangXiangPHZ.prototype.clearTrustTimer = function(tb, pl) {

    if (pl.trustTimer_bp) {

        clearTimeout(pl.trustTimer_bp);
        pl.trustTimer_bp = null;

        pl.trustEnd = -1;
        if (tb.AllPlayerCheck(function(p) { return p.trustEnd == -1; })) {
            tb.tData.trustEnd = -1;
            tb.NotifyAll("trustTime", {trustEnd: tb.tData.trustEnd});
        }
    }

    if (pl.trustTimer_bp2) {
        clearTimeout(pl.trustTimer_bp2);
        pl.trustTimer_bp2 = null;
    }
};

// 手动托管
GameCodeXiangXiangPHZ.prototype.beTrust = function(pl, msg, session, next, tb) {

    if (pl.trust) {
        return;
    }

    this.clearTrustTimer(tb, pl);
    pl.trust = true;
    tb.NotifyAll('beTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'beTrust', {uid: pl.uid});

    if (this.isWaitOperate(tb, null)) { // 等待自动提偎跑 不要走托管倒计时
        this.doTrustAction(tb, pl);
    }
};

// 取消托管接口(正常情况其它请求入口被屏蔽 只能通过此接口取消托管)
GameCodeXiangXiangPHZ.prototype.cancelTrust = function(pl, msg, session, next, tb) {

    logger.debug("fangpaofa", "cancel trust 1");
    if (!pl.trust) {
        return;
    }

    logger.debug("fangpaofa", "cancel trust 2");

    this.clearTrustTimer(tb, pl);
    pl.trust = false;
    tb.NotifyAll('cancelTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'cancelTrust', {uid: pl.uid});

    if (this.isWaitOperate(tb, null)) { // 等待自动提偎跑 不要走托管倒计时

        this.checkTrust(tb, pl);
    }
};

// 容错处理 手动操作请求（吃 出牌 过）取消托管状态
GameCodeXiangXiangPHZ.prototype.checkCancelTrust = function(pl, msg, session, next, tb) {

    if (!pl.trust || msg.byTrust) {
        return;
    }

    pl.trust = false;
    tb.NotifyAll('cancelTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'cancelTrust', {uid: pl.uid});
};

module.exports = GameCodeXiangXiangPHZ;
