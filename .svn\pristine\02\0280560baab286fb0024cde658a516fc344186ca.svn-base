/* automatically generated by JSCoverage - do not edit */
if (typeof _$jscoverage === 'undefined') _$jscoverage = {};
if (! _$jscoverage['appenders/multiprocess.js']) {
  _$jscoverage['appenders/multiprocess.js'] = [];
  _$jscoverage['appenders/multiprocess.js'][1] = 0;
  _$jscoverage['appenders/multiprocess.js'][2] = 0;
  _$jscoverage['appenders/multiprocess.js'][11] = 0;
  _$jscoverage['appenders/multiprocess.js'][17] = 0;
  _$jscoverage['appenders/multiprocess.js'][18] = 0;
  _$jscoverage['appenders/multiprocess.js'][19] = 0;
  _$jscoverage['appenders/multiprocess.js'][20] = 0;
  _$jscoverage['appenders/multiprocess.js'][21] = 0;
  _$jscoverage['appenders/multiprocess.js'][22] = 0;
  _$jscoverage['appenders/multiprocess.js'][25] = 0;
  _$jscoverage['appenders/multiprocess.js'][33] = 0;
  _$jscoverage['appenders/multiprocess.js'][34] = 0;
  _$jscoverage['appenders/multiprocess.js'][36] = 0;
  _$jscoverage['appenders/multiprocess.js'][39] = 0;
  _$jscoverage['appenders/multiprocess.js'][41] = 0;
  _$jscoverage['appenders/multiprocess.js'][42] = 0;
  _$jscoverage['appenders/multiprocess.js'][44] = 0;
  _$jscoverage['appenders/multiprocess.js'][45] = 0;
  _$jscoverage['appenders/multiprocess.js'][46] = 0;
  _$jscoverage['appenders/multiprocess.js'][50] = 0;
  _$jscoverage['appenders/multiprocess.js'][51] = 0;
  _$jscoverage['appenders/multiprocess.js'][52] = 0;
  _$jscoverage['appenders/multiprocess.js'][53] = 0;
  _$jscoverage['appenders/multiprocess.js'][54] = 0;
  _$jscoverage['appenders/multiprocess.js'][55] = 0;
  _$jscoverage['appenders/multiprocess.js'][56] = 0;
  _$jscoverage['appenders/multiprocess.js'][58] = 0;
  _$jscoverage['appenders/multiprocess.js'][62] = 0;
  _$jscoverage['appenders/multiprocess.js'][63] = 0;
  _$jscoverage['appenders/multiprocess.js'][66] = 0;
  _$jscoverage['appenders/multiprocess.js'][68] = 0;
  _$jscoverage['appenders/multiprocess.js'][71] = 0;
  _$jscoverage['appenders/multiprocess.js'][72] = 0;
  _$jscoverage['appenders/multiprocess.js'][76] = 0;
  _$jscoverage['appenders/multiprocess.js'][78] = 0;
  _$jscoverage['appenders/multiprocess.js'][79] = 0;
  _$jscoverage['appenders/multiprocess.js'][80] = 0;
  _$jscoverage['appenders/multiprocess.js'][81] = 0;
  _$jscoverage['appenders/multiprocess.js'][82] = 0;
  _$jscoverage['appenders/multiprocess.js'][84] = 0;
  _$jscoverage['appenders/multiprocess.js'][86] = 0;
  _$jscoverage['appenders/multiprocess.js'][89] = 0;
  _$jscoverage['appenders/multiprocess.js'][90] = 0;
  _$jscoverage['appenders/multiprocess.js'][91] = 0;
  _$jscoverage['appenders/multiprocess.js'][92] = 0;
  _$jscoverage['appenders/multiprocess.js'][96] = 0;
  _$jscoverage['appenders/multiprocess.js'][97] = 0;
  _$jscoverage['appenders/multiprocess.js'][98] = 0;
  _$jscoverage['appenders/multiprocess.js'][101] = 0;
  _$jscoverage['appenders/multiprocess.js'][102] = 0;
  _$jscoverage['appenders/multiprocess.js'][103] = 0;
  _$jscoverage['appenders/multiprocess.js'][105] = 0;
  _$jscoverage['appenders/multiprocess.js'][110] = 0;
  _$jscoverage['appenders/multiprocess.js'][111] = 0;
  _$jscoverage['appenders/multiprocess.js'][112] = 0;
  _$jscoverage['appenders/multiprocess.js'][114] = 0;
  _$jscoverage['appenders/multiprocess.js'][118] = 0;
  _$jscoverage['appenders/multiprocess.js'][119] = 0;
  _$jscoverage['appenders/multiprocess.js'][120] = 0;
  _$jscoverage['appenders/multiprocess.js'][121] = 0;
  _$jscoverage['appenders/multiprocess.js'][122] = 0;
  _$jscoverage['appenders/multiprocess.js'][123] = 0;
  _$jscoverage['appenders/multiprocess.js'][125] = 0;
  _$jscoverage['appenders/multiprocess.js'][128] = 0;
  _$jscoverage['appenders/multiprocess.js'][129] = 0;
}
_$jscoverage['appenders/multiprocess.js'][1]++;
"use strict";
_$jscoverage['appenders/multiprocess.js'][2]++;
var log4js = require("../log4js"), net = require("net"), END_MSG = "__LOG4JS__";
_$jscoverage['appenders/multiprocess.js'][11]++;
function logServer(config) {
  _$jscoverage['appenders/multiprocess.js'][17]++;
  function deserializeLoggingEvent(clientSocket, msg) {
    _$jscoverage['appenders/multiprocess.js'][18]++;
    var loggingEvent;
    _$jscoverage['appenders/multiprocess.js'][19]++;
    try {
      _$jscoverage['appenders/multiprocess.js'][20]++;
      loggingEvent = JSON.parse(msg);
      _$jscoverage['appenders/multiprocess.js'][21]++;
      loggingEvent.startTime = new Date(loggingEvent.startTime);
      _$jscoverage['appenders/multiprocess.js'][22]++;
      loggingEvent.level = log4js.levels.toLevel(loggingEvent.level.levelStr);
    }
    catch (e) {
      _$jscoverage['appenders/multiprocess.js'][25]++;
      loggingEvent = {startTime: new Date(), categoryName: "log4js", level: log4js.levels.ERROR, data: ["Unable to parse log:", msg]};
    }
    _$jscoverage['appenders/multiprocess.js'][33]++;
    loggingEvent.remoteAddress = clientSocket.remoteAddress;
    _$jscoverage['appenders/multiprocess.js'][34]++;
    loggingEvent.remotePort = clientSocket.remotePort;
    _$jscoverage['appenders/multiprocess.js'][36]++;
    return loggingEvent;
}
  _$jscoverage['appenders/multiprocess.js'][39]++;
  var actualAppender = config.actualAppender, server = net.createServer((function serverCreated(clientSocket) {
  _$jscoverage['appenders/multiprocess.js'][41]++;
  clientSocket.setEncoding("utf8");
  _$jscoverage['appenders/multiprocess.js'][42]++;
  var logMessage = "";
  _$jscoverage['appenders/multiprocess.js'][44]++;
  function logTheMessage(msg) {
    _$jscoverage['appenders/multiprocess.js'][45]++;
    if (logMessage.length > 0) {
      _$jscoverage['appenders/multiprocess.js'][46]++;
      actualAppender(deserializeLoggingEvent(clientSocket, msg));
    }
}
  _$jscoverage['appenders/multiprocess.js'][50]++;
  function chunkReceived(chunk) {
    _$jscoverage['appenders/multiprocess.js'][51]++;
    var event;
    _$jscoverage['appenders/multiprocess.js'][52]++;
    logMessage += chunk || "";
    _$jscoverage['appenders/multiprocess.js'][53]++;
    if (logMessage.indexOf(END_MSG) > -1) {
      _$jscoverage['appenders/multiprocess.js'][54]++;
      event = logMessage.substring(0, logMessage.indexOf(END_MSG));
      _$jscoverage['appenders/multiprocess.js'][55]++;
      logTheMessage(event);
      _$jscoverage['appenders/multiprocess.js'][56]++;
      logMessage = logMessage.substring(event.length + END_MSG.length) || "";
      _$jscoverage['appenders/multiprocess.js'][58]++;
      chunkReceived();
    }
}
  _$jscoverage['appenders/multiprocess.js'][62]++;
  clientSocket.on("data", chunkReceived);
  _$jscoverage['appenders/multiprocess.js'][63]++;
  clientSocket.on("end", chunkReceived);
}));
  _$jscoverage['appenders/multiprocess.js'][66]++;
  server.listen(config.loggerPort || 5000, config.loggerHost || "localhost");
  _$jscoverage['appenders/multiprocess.js'][68]++;
  return actualAppender;
}
_$jscoverage['appenders/multiprocess.js'][71]++;
function workerAppender(config) {
  _$jscoverage['appenders/multiprocess.js'][72]++;
  var canWrite = false, buffer = [], socket;
  _$jscoverage['appenders/multiprocess.js'][76]++;
  createSocket();
  _$jscoverage['appenders/multiprocess.js'][78]++;
  function createSocket() {
    _$jscoverage['appenders/multiprocess.js'][79]++;
    socket = net.createConnection(config.loggerPort || 5000, config.loggerHost || "localhost");
    _$jscoverage['appenders/multiprocess.js'][80]++;
    socket.on("connect", (function () {
  _$jscoverage['appenders/multiprocess.js'][81]++;
  emptyBuffer();
  _$jscoverage['appenders/multiprocess.js'][82]++;
  canWrite = true;
}));
    _$jscoverage['appenders/multiprocess.js'][84]++;
    socket.on("timeout", socket.end.bind(socket));
    _$jscoverage['appenders/multiprocess.js'][86]++;
    socket.on("close", createSocket);
}
  _$jscoverage['appenders/multiprocess.js'][89]++;
  function emptyBuffer() {
    _$jscoverage['appenders/multiprocess.js'][90]++;
    var evt;
    _$jscoverage['appenders/multiprocess.js'][91]++;
    while ((evt = buffer.shift())) {
      _$jscoverage['appenders/multiprocess.js'][92]++;
      write(evt);
}
}
  _$jscoverage['appenders/multiprocess.js'][96]++;
  function write(loggingEvent) {
    _$jscoverage['appenders/multiprocess.js'][97]++;
    socket.write(JSON.stringify(loggingEvent), "utf8");
    _$jscoverage['appenders/multiprocess.js'][98]++;
    socket.write(END_MSG, "utf8");
}
  _$jscoverage['appenders/multiprocess.js'][101]++;
  return (function log(loggingEvent) {
  _$jscoverage['appenders/multiprocess.js'][102]++;
  if (canWrite) {
    _$jscoverage['appenders/multiprocess.js'][103]++;
    write(loggingEvent);
  }
  else {
    _$jscoverage['appenders/multiprocess.js'][105]++;
    buffer.push(loggingEvent);
  }
});
}
_$jscoverage['appenders/multiprocess.js'][110]++;
function createAppender(config) {
  _$jscoverage['appenders/multiprocess.js'][111]++;
  if (config.mode === "master") {
    _$jscoverage['appenders/multiprocess.js'][112]++;
    return logServer(config);
  }
  else {
    _$jscoverage['appenders/multiprocess.js'][114]++;
    return workerAppender(config);
  }
}
_$jscoverage['appenders/multiprocess.js'][118]++;
function configure(config, options) {
  _$jscoverage['appenders/multiprocess.js'][119]++;
  var actualAppender;
  _$jscoverage['appenders/multiprocess.js'][120]++;
  if (config.appender && config.mode === "master") {
    _$jscoverage['appenders/multiprocess.js'][121]++;
    log4js.loadAppender(config.appender.type);
    _$jscoverage['appenders/multiprocess.js'][122]++;
    actualAppender = log4js.appenderMakers[config.appender.type](config.appender, options);
    _$jscoverage['appenders/multiprocess.js'][123]++;
    config.actualAppender = actualAppender;
  }
  _$jscoverage['appenders/multiprocess.js'][125]++;
  return createAppender(config);
}
_$jscoverage['appenders/multiprocess.js'][128]++;
exports.appender = createAppender;
_$jscoverage['appenders/multiprocess.js'][129]++;
exports.configure = configure;
_$jscoverage['appenders/multiprocess.js'].source = ["\"use strict\";","var log4js = require('../log4js')",", net = require('net')",", END_MSG = '__LOG4JS__';","","/**"," * Creates a server, listening on config.loggerPort, config.loggerHost."," * Output goes to config.actualAppender (config.appender is used to"," * set up that appender)."," */","function logServer(config) {","  ","  /**","   * Takes a utf-8 string, returns an object with","   * the correct log properties.","   */","  function deserializeLoggingEvent(clientSocket, msg) {","    var loggingEvent;","    try {","      loggingEvent = JSON.parse(msg);","      loggingEvent.startTime = new Date(loggingEvent.startTime);","      loggingEvent.level = log4js.levels.toLevel(loggingEvent.level.levelStr);","    } catch (e) {","      // JSON.parse failed, just log the contents probably a naughty.","      loggingEvent = {","        startTime: new Date(),","        categoryName: 'log4js',","        level: log4js.levels.ERROR,","        data: [ 'Unable to parse log:', msg ]","      };","    }","","    loggingEvent.remoteAddress = clientSocket.remoteAddress;","    loggingEvent.remotePort = clientSocket.remotePort;","    ","    return loggingEvent;","  }","  ","  var actualAppender = config.actualAppender,","  server = net.createServer(function serverCreated(clientSocket) {","    clientSocket.setEncoding('utf8');","    var logMessage = '';","    ","    function logTheMessage(msg) {","      if (logMessage.length &gt; 0) {","        actualAppender(deserializeLoggingEvent(clientSocket, msg));","      }","    }","    ","    function chunkReceived(chunk) {","      var event;","      logMessage += chunk || '';","      if (logMessage.indexOf(END_MSG) &gt; -1) {","        event = logMessage.substring(0, logMessage.indexOf(END_MSG));","        logTheMessage(event);","        logMessage = logMessage.substring(event.length + END_MSG.length) || '';","        //check for more, maybe it was a big chunk","        chunkReceived();","      }","    }","    ","    clientSocket.on('data', chunkReceived);","    clientSocket.on('end', chunkReceived);","  });","  ","  server.listen(config.loggerPort || 5000, config.loggerHost || 'localhost');","  ","  return actualAppender;","}","","function workerAppender(config) {","  var canWrite = false,","  buffer = [],","  socket;","  ","  createSocket();","  ","  function createSocket() {","    socket = net.createConnection(config.loggerPort || 5000, config.loggerHost || 'localhost');","    socket.on('connect', function() {","      emptyBuffer();","      canWrite = true;","    });","    socket.on('timeout', socket.end.bind(socket));","    //don't bother listening for 'error', 'close' gets called after that anyway","    socket.on('close', createSocket);","  }","  ","  function emptyBuffer() {","    var evt;","    while ((evt = buffer.shift())) {","      write(evt);","    }","  }","  ","  function write(loggingEvent) {","    socket.write(JSON.stringify(loggingEvent), 'utf8');","    socket.write(END_MSG, 'utf8');","  }","  ","  return function log(loggingEvent) {","    if (canWrite) {","      write(loggingEvent);","    } else {","      buffer.push(loggingEvent);","    }","  };","}","","function createAppender(config) {","  if (config.mode === 'master') {","    return logServer(config);","  } else {","    return workerAppender(config);","  }","}","","function configure(config, options) {","  var actualAppender;","  if (config.appender &amp;&amp; config.mode === 'master') {","    log4js.loadAppender(config.appender.type);","    actualAppender = log4js.appenderMakers[config.appender.type](config.appender, options);","    config.actualAppender = actualAppender;","  }","  return createAppender(config);","}","","exports.appender = createAppender;","exports.configure = configure;"];
