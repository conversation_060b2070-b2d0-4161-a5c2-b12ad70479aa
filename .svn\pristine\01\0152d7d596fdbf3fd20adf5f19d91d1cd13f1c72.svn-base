function GameCodeYongZhouLaoChuo(majiang, app) {
    this.majiang = majiang;
    this.app = app;
}

GameCodeYongZhouLaoChuo.prototype.initAreaSelectMode = function(tb) {
    var tData = tb.tData;
    tData.maxPlayer = tb.createParams.maxPlayer;//人数
    tData.minChuo = tb.createParams.maxPlayer == 4 ? 11 : [14, 15][tb.createParams.chuoNum];
    tData.areaSelectMode.chuoType = tb.createParams.chuoType; // 0曲戳/1定戳
    tData.areaSelectMode.chuoNum = tb.createParams.chuoNum; // 放戳 0/1
    tData.areaSelectMode.isJianHongJiaFen = tb.createParams.isJianHongJiaFen; // 见红加分 true/false
    tData.areaSelectMode.isQiHuErFen = tb.createParams.isQiHuErFen; // 起胡2分 true/false
    tData.areaSelectMode.isHongChuoSiFan = tb.createParams.isHongChuoSiFan; // 红戳4番 true/false
    tData.areaSelectMode.isFanChuo = tb.createParams.isFanChuo && tData.areaSelectMode.chuoType == 0; // 番戳 true/false
    tData.areaSelectMode.trustTime = tb.createParams.trustTime > 0 ? tb.createParams.trustTime : -1;//托管倒计时
    tData.areaSelectMode.trustWay = tb.createParams.trustWay || 0;//托管方式 0当局/1两局/2全局
    tData.areaSelectMode.jieSuanDiFen = tb.createParams.jieSuanDiFen || 1;// 结算底分
    //tData.areaSelectMode.isTrustWhole = this.createParams.isTrustWhole;//全局托管
    tData.gameCnName = "永州老戳";
};

GameCodeYongZhouLaoChuo.prototype.initPlayerOnce = function(pl, tb) {
    pl.roomStatistics = [0, 0, 0, 0, 0];

    var tData = tb.tData;
    pl.trust = false;        // 是否在托管状态
    tData.trustEnd = -1; // 等操作玩家 进入托管状态倒计时
};

GameCodeYongZhouLaoChuo.prototype.initPlayer = function(pl) {
    pl.lastPutCard = [];//当前轮打出的牌
    pl.chuoCards = [];// 当局的戳牌 二维

    pl.trustEnd = -1;        // 进入托管状态倒计时
}

GameCodeYongZhouLaoChuo.prototype.collectPlayer = function(tb) {
    var ptyList = [
        'info', 'mjState', 'mjput', 'onLine', 'delRoom', 'winall',  'tableMsg', 'locationMsg', 'roomStatistics', 'lastOffLineTime','winone',
        'lastPutCard', 'chuoCards','eatFlag','trust'
    ];
    return tb.collectPlayer.apply(tb, ptyList);
}

GameCodeYongZhouLaoChuo.prototype.doBeforeHands = function(tb) { // 发牌前字段重置 通过mjhand消息同步tData
    var tData = tb.tData;
    tb.AllPlayerRun((p)=> {
        if (!p.trustWholeTimes || p.trustWholeTimes <= 0) {
            p.trustWholeTimes = [1,2,100][tData.areaSelectMode.trustWay || 0];
        }
    });
}

GameCodeYongZhouLaoChuo.prototype.doAfterHands = function(tb) {
    var tData = tb.tData;
    
    var msg = {
        chuoCards: tb.PlayerPtys(function (p) {
            return p.chuoCards;
        }),
    };
    tb.NotifyAll("HZUpdateData", msg);
    cloneDataAndPush(tb.mjlog, "HZUpdateData", msg);

    tData.lastPutCard = null;//当前桌面上的牌
    tData.lastPutPlayer = -1;//上一个出牌玩家
    tData.curStartPlayer = -1;//当前轮的开始玩家
    tData.tState = TableState.waitPut;
    tData.curPlayer = tData.zhuang - 1;
    this.turnNextPl(tb);
};

GameCodeYongZhouLaoChuo.prototype.checkEnd = function(tb) {
    if(!tb.AllPlayerCheck((p)=>{
            return p.mjhand.length != 0;
        })){
        this.EndGame(tb, null);
        return true;
    }
    return false;
};

GameCodeYongZhouLaoChuo.prototype.newTurn = function(tb) {
    var tData = tb.tData;
    tData.tState = TableState.waitPut;
    tData.curStartPlayer = tData.curPlayer;
    tb.NotifyAll('waitPut', tData);
    cloneDataAndPush(tb.mjlog, 'waitPut', tData);
    //托管
    var pl = tb.getPlayer(tData.uids[tData.curPlayer]);
    this.checkTrust(tb, pl);
};

GameCodeYongZhouLaoChuo.prototype.turnNextPl = function(tb) {
    var tData = tb.tData;
    tData.curPlayer = (tData.curPlayer + 1) % tData.maxPlayer;

    if(tData.curStartPlayer == -1){//开局打牌
        this.newTurn(tb);
    }else if(tData.curPlayer == tData.curStartPlayer){//当前回合结束
        tData.curPlayer = tData.lastPutPlayer;
        var pl = tb.getPlayer(tData.uids[tData.curPlayer]);
        pl.chuoCards.push(pl.lastPutCard);
        tb.AllPlayerRun((p)=>{
            p.lastPutCard = [];
        });
        tData.lastPutCard = null;
        var msg = {
            chuoCards: tb.PlayerPtys(function (p) {
                return p.chuoCards;
            }),
        };
        tb.NotifyAll("HZUpdateData", msg);
        cloneDataAndPush(tb.mjlog, "HZUpdateData", msg);

        if(this.majiang.canHu(tb, pl)){
            tData.tState = TableState.waitEat;
            pl.eatFlag = 32;
            if(pl.mjhand.length == 0){
                pl.eatFlag += 256;
            }
            var msg = {
                eatFlags: tb.PlayerPtys(function (p) {
                    return p.eatFlag;
                }),
                tData: tb.tData
            };
            pl.notify("HZUpdateEatFlag", msg);
            cloneDataAndPush(tb.mjlog, "HZUpdateEatFlag", msg);
            //托管
            this.checkTrust(tb, pl);
        }else{
            if(!this.checkEnd(tb)){
                this.newTurn(tb);
            }
        }
    }else{
        var pl = tb.getPlayer(tData.uids[tData.curPlayer]);
        if(this.majiang.canBeat(pl.mjhand, tData.lastPutCard, tData.areaSelectMode)){
            tb.NotifyAll('waitPut', tData);
            cloneDataAndPush(tb.mjlog, 'waitPut', tData);
            //托管
            this.checkTrust(tb, pl);
        }else{
            this.pkPassCard(pl, {}, null, null, tb);
        }
    }
}

// 是否需要手动准备
GameCodeYongZhouLaoChuo.prototype.isBtnReady = function(tb) {
    return true;
};

GameCodeYongZhouLaoChuo.prototype.pkPutCard = function(pl, msg, session, next, tb) {
    var putCard = msg.card;
    var tData = tb.tData;
    if (!Array.isArray(putCard) || putCard.length == 0) {
        return;
    }

    if (!(tData.tState == TableState.waitPut && pl.uid == tData.uids[tData.curPlayer])) {
        return;
    }

    this.clearTrustTimer(tb, pl);

    var handDict = {};
    var putDict = {};
    for (var i = 0; i < pl.mjhand.length; i++) {
        var cd = pl.mjhand[i];
        handDict[cd] = handDict[cd] ? handDict[cd] + 1 : 1;
    }

    for (var i = 0; i < putCard.length; i++) {
        var cd = putCard[i];
        putDict[cd] = putDict[cd] ? putDict[cd] + 1 : 1;
    }

    for (var k in putDict) {
        if (!handDict[k] || handDict[k] < putDict[k]) { // 打的牌不存在
            return;
        }
    }

    if (tData.lastPutCard) {
        if (!this.majiang.isBigger(putCard, tData.lastPutCard, tData.areaSelectMode)) { // 大不起
            return;
        }
    }
    tData.lastPutCard = putCard;
    tData.lastPutPlayer = tData.curPlayer;
    pl.lastPutCard = putCard;

    for (var i = 0; i < putCard.length; i++) {
        pl.mjhand.splice(pl.mjhand.indexOf(putCard[i]), 1);
    }

    var ret = false;
    var endIndex = (tData.curStartPlayer + tData.maxPlayer - 1)%tData.maxPlayer;
    var selfIndex = tData.uids.indexOf(pl.uid);
    for(var i = 1 ; i < tData.maxPlayer; i++){
        if(selfIndex != endIndex){
            var p =  tb.getPlayer(tData.uids[(selfIndex + i)%tData.maxPlayer])
            if(this.majiang.canBeat(p.mjhand, tData.lastPutCard, tData.areaSelectMode)){
                ret = true;
                break;
            }

            if(endIndex == ((selfIndex + i)%tData.maxPlayer)){
                break;
            }
        }else{
            break;
        }
    }

    var msg = {
        uid: pl.uid,
        card: putCard,
        canBeat: ret,
    };
    tb.NotifyAll('PKPut', msg);
    cloneDataAndPush(tb.mjlog, 'PKPut', msg);

    this.turnNextPl(tb);
};

GameCodeYongZhouLaoChuo.prototype.pkPassCard = function(pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState == TableState.waitPut) {
        if (!tData.lastPutCard || pl.uid != tData.uids[tData.curPlayer]) {
            return;
        }
        //自动过，取消定时器，托管状态不能改变
        this.clearTrustTimer(tb, pl);
        //this.checkCancelTrust(pl, msg, session, next, tb);
        /*if (this.majiang.canBeat(pl.mjhand, tData.lastPutCard, tData.areaSelectMode)) {
            return;
        }*/

        var msg = {
            uid: pl.uid
        };
        tb.NotifyAll("PKPass", msg);
        cloneDataAndPush(tb.mjlog, "PKPass", msg);
         
        this.turnNextPl(tb);

    } else if(tData.tState == TableState.waitEat){
        this.clearTrustTimer(tb, pl);
        this.checkCancelTrust(pl, msg, session, next, tb);

        if(pl.eatFlag & 32 == 0 || pl.eatFlag & 256 == 1){
            return;
        }
        pl.eatFlag = 0;
        if(!this.checkEnd(tb)){
            this.newTurn(tb);
        }
    } else if (tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish || tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady) {
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', {uid: pl.uid, onLine: true, mjState: pl.mjState, isTrust: msg.isTrust});
        var tData = tb.tData;
        if (tb.PlayerCount() == tData.maxPlayer && tb.AllPlayerCheck(function(p) {return p.mjState == TableState.isReady;})) {
            if(tData.tState == TableState.roundFinish){
                this.countZhuang(tData, tb);
            }
            tb.runStartGame();
        }
    }
}

GameCodeYongZhouLaoChuo.prototype.mjHuCard = function(pl, msg, session, next, tb) {
    var tData = tb.tData;
    if (tData.tState != TableState.waitEat || ((pl.eatFlag & 32) == 0)) {
        return;
    }
    this.clearTrustTimer(tb, pl);
    this.checkCancelTrust(pl, msg, session, next, tb);

    this.doHuCard(pl, tb);
};

GameCodeYongZhouLaoChuo.prototype.doHuCard = function(pl, tb) {
    pl.roomStatistics[0]++;
    var msg = {uid: pl.uid};
    tb.NotifyAll("MJHu", msg);
    cloneDataAndPush(tb.mjlog, "MJHu", msg);

    this.EndGame(tb, pl);
};

GameCodeYongZhouLaoChuo.prototype.huScore = function(tb, pl) {
    var tData = tb.tData;

    var huInfo = this.majiang.getHuInfo(tb, pl, null);
    tData.winner = tData.uids.indexOf(pl.uid);

    pl.handSort = huInfo.handSort;
    pl.hzdesc = huInfo.hzdesc;
    pl.hzdesc.chuoCount = huInfo.chuoCount;
    pl.hzdesc.baseScore = huInfo.baseScore;
    pl.hzdesc.hongScore = huInfo.hongScore;
    var score = huInfo.score;
    tb.AllPlayerRun((p)=>{
        if (p != pl) {
            pl.winone += score;
            p.winone -= score;
        }
    });
    tb.AllPlayerRun(function(p){
        p.winone *= tb.tData.areaSelectMode.jieSuanDiFen;
        p.winone = revise(p.winone);
    }.bind(this));
};

GameCodeYongZhouLaoChuo.prototype.EndRoom = function(tb, msg) {
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function(p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.windif = p.windif;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({userId: p.uid, lastGameTime: p.info.lastGameTime});
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if(!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;

            msg.players = tb.collectPlayer('lastOffLineTime', 'winall');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeYongZhouLaoChuo.prototype.EndGame = function(tb, pl, byEndRoom) {
    var tData = tb.tData;
    tb.AllPlayerRun((p)=>{
        this.clearTrustTimer(tb, p); // 解散
        if(tData.areaSelectMode.trustTime > 0 && !tData.areaSelectMode.isTrustWhole){
            //p.trust = false;
        }
        p.mjState = TableState.roundFinish;
    });
    tData.isDismiss = byEndRoom;
    if (byEndRoom) {
        tb.showDissmissDesc();
    }
    if (pl) {
        this.huScore(tb, pl);
    } else {
        tData.winner = -1;
    }
    
    tb.AllPlayerRun(function(p) {
        p.tableMsg.push(p.winone);
    });
    tData.tState = TableState.roundFinish;
    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }
    tb.AllPlayerRun(function(p) {
        p.winall += p.winone;
        p.winall = revise(p.winall);
    });
    tb.perRoundEndDo();
    //降低免扣功能数据统计
    tb.statisticsAnalyse();

    tData.roundNum--;
    tb.AllPlayerRun((p)=>{
        if(p.trust){
            if(--p.trustWholeTimes > 0){
                tb.trustWhole(p);
            }else{
                this.cancelTrust(p, {}, null, function() {}, tb)
            }
        }else{
            tb.trustWhole(p);
        }
    });
    var roundEnd = {
        players: tb.collectPlayer('mjhand', 'mjdesc', 'winone', 'winall', 'winType', 'baseWin', "mjwei", "mjsort", "handSort", "hzdesc", 'mjflower',
            'info', "tableMsg", 'lastOffLineTime', 'roomStatistics'
        ),
        tData: tData,
        cards: tb.cards,
        hunCard: tData.hunCard,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };
    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);
    var playInfo = null;
    if (tData.roundNum == 0) {
        playInfo = this.EndRoom(tb);
        roundEnd.playInfo = playInfo;
    }

    tb.NotifyAll("roundEnd", roundEnd);
}

GameCodeYongZhouLaoChuo.prototype.DestroyTable = function(tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
};

GameCodeYongZhouLaoChuo.prototype.countZhuang = function(tData, tb) {
    if (tData.roundNum == tData.roundAll) {
        tData.zhuang = tData.uids.indexOf(tData.uidsQueue[0]);
    } else if (tData.winner != -1) {
        tData.zhuang = tData.winner;
    } else {
        tb.AllPlayerRun((p)=>{
            if(p.mjhand.length == 0){
                tData.zhuang = tData.uids.indexOf(p.uid);
            }
        });
    }

    tData.curPlayer = tData.zhuang;
};

GameCodeYongZhouLaoChuo.prototype.isWaitOperate = function(tb, autoHuUid) {
    var tData = tb.tData;

    if (tData.tState == TableState.waitCard) {
        return false;
    }

    if (tData.tState == TableState.waitPut) {
        return true;
    }

    if (autoHuUid) {
        return false;
    }

    return true;
};

GameCodeYongZhouLaoChuo.prototype.doTrustAction = function(tb, pl) {
    var tData = tb.tData;
    if (tData.tState == TableState.waitPut) {
        var card = this.majiang.getTipCards(pl, tData.lastPutCard, tData.areaSelectMode);
        if(card.length > 0){
            this.pkPutCard(pl, {card: card, byTrust: true}, null, function() {}, tb);
        }
    } else if (tData.tState == TableState.waitEat) {
        if (pl.eatFlag & 32) {
            this.mjHuCard(pl, {byTrust: true}, null, function() {}, tb);
        }
    }
};

GameCodeYongZhouLaoChuo.prototype.doTrustCountdown = function(tb, pl) {
    if (pl.trustTimer_bp2) { // 容错处理
        clearTimeout(pl.trustTimer_bp2);
    }
    pl.trustTimer_bp2 = setTimeout(function () {
        pl.trustTimer_bp2 = null;
        this.doTrustAction(tb, pl);
    }.bind(this), 1100);
};

GameCodeYongZhouLaoChuo.prototype.beTrustCountdown = function(tb, pl) {
    var tData = tb.tData;
    if (tData.trustEnd == -1) {
        tData.trustEnd = Date.now() + tData.areaSelectMode.trustTime * 1000;
        tb.NotifyAll("trustTime", {trustEnd: tData.trustEnd});
    }

    pl.trustEnd = tData.trustEnd;
    if (pl.trustTimer_bp) {
        clearTimeout(pl.trustTimer_bp);
    }
    pl.trustTimer_bp = setTimeout(function () {
        pl.trustTimer_bp = null;
        pl.trust = true;

        tb.NotifyAll("beTrust", {uid: pl.uid, type : 0});
        cloneDataAndPush(tb.mjlog, 'beTrust', {uid: pl.uid, type : 0});
        tData.trustEnd = -1;
        pl.trustEnd = -1;
        this.doTrustCountdown(tb, pl);
    }.bind(this), tData.areaSelectMode.trustTime * 1000);
};

GameCodeYongZhouLaoChuo.prototype.checkTrust = function(tb, pl) {
    if (!(tb.tData.areaSelectMode.trustTime > 0)) {
        return;
    }

    if (pl.uid != tb.tData.uids[tb.tData.curPlayer] || pl.mjState == TableState.roundFinish) {
        return;
    }

    pl.trust ? this.doTrustCountdown(tb, pl) : this.beTrustCountdown(tb, pl);
};

GameCodeYongZhouLaoChuo.prototype.clearTrustTimer = function(tb, pl) {
    if (pl.trustTimer_bp) {

        clearTimeout(pl.trustTimer_bp);
        pl.trustTimer_bp = null;

        // pl.trust = false;
        pl.trustEnd = -1;
        if (tb.AllPlayerCheck(function(p) {
                return p.trustEnd == -1
            })) {
            tb.tData.trustEnd = -1;
            tb.NotifyAll("trustTime", {trustEnd: tb.tData.trustEnd});
        }
    }

    if (pl.trustTimer_bp2) {
        clearTimeout(pl.trustTimer_bp2);
        pl.trustTimer_bp2 = null;
    }
};

// 手动托管
GameCodeYongZhouLaoChuo.prototype.beTrust = function(pl, msg, session, next, tb) {
    if (pl.trust) {
        return;
    }

    this.clearTrustTimer(tb, pl);
    pl.trust = true;
    tb.NotifyAll('beTrust', {uid: pl.uid, type : 1});
    cloneDataAndPush(tb.mjlog, 'beTrust', {uid: pl.uid, type : 1});

    if (this.isWaitOperate(tb, null)) { // 等待自动提偎跑 不要走托管倒计时
        this.doTrustAction(tb, pl);
        // this.checkTrust(tb, pl);
    }
};

// 取消托管接口(正常情况其它请求入口被屏蔽 只能通过此接口取消托管)
GameCodeYongZhouLaoChuo.prototype.cancelTrust = function(pl, msg, session, next, tb) {
    if (!pl.trust) {
        return;
    }
    pl.trustWholeTimes = [1,2,100][tb.tData.areaSelectMode.trustWay || 0];
    this.clearTrustTimer(tb, pl);
    pl.trust = false;
    tb.NotifyAll('cancelTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'cancelTrust', {uid: pl.uid});

    if (this.isWaitOperate(tb, null)) { // 等待自动提偎跑 不要走托管倒计时
        this.checkTrust(tb, pl);
    }
};

// 容错处理 手动操作请求（吃 出牌 过）取消托管状态
GameCodeYongZhouLaoChuo.prototype.checkCancelTrust = function(pl, msg, session, next, tb) {
    if (!pl.trust || msg.byTrust) {
        return;
    }

    pl.trust = false;
    tb.NotifyAll('cancelTrust', {uid: pl.uid});
    cloneDataAndPush(tb.mjlog, 'cancelTrust', {uid: pl.uid});
};

// 托管逻辑
GameCodeYongZhouLaoChuo.prototype.beTrustLogic = function(pl, tb) {

};

GameCodeYongZhouLaoChuo.prototype.checkTrustWholeStatus = function(pl, tb){
    return pl.mjState == TableState.roundFinish;
};

GameCodeYongZhouLaoChuo.prototype.trustWholeAction = function(pl, tb){
    if(pl.mjState == TableState.roundFinish){
        var cardNext = tb.tData.cardNext;
        tb.GameReady(pl, {isTrust: true, cardNext: cardNext}, null, function() {});
    }
};

GameCodeYongZhouLaoChuo.prototype.trustWholeDelay = function(tb) {
    return 3; // 小结算停留3s
};

module.exports = GameCodeYongZhouLaoChuo;

