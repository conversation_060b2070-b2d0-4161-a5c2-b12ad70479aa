
var flowerCards = [31, 41, 51, 61, 71, 81, 91,111, 121, 131, 141, 151, 161, 171, 181];

function GameCodeganyu(majiang, app) {
    this.majiang = majiang;
    this.app = app;
}

GameCodeganyu.prototype.initPlayer = function(pl) {
    pl.putCount = 0;                        //出牌数
    pl.isTianting = false;                  //天听
};


// 获取胡的类型, 返回true还是false
GameCodeganyu.prototype.getHuType = function(pl, cd) {
    logger.debug("=====getHuType=====11111");
    var canHu = this.majiang.canHu(pl.mjhand, cd);
    logger.debug("getHuType=====gpl.uid:" + pl.uid + ",getHuType huState:" + canHu);
    return canHu;
};

// 发牌
GameCodeganyu.prototype.sendNewCard = function(tb) {
    logger.debug("sendNewCard=====sendNewCard=====");
    var tData = tb.tData;
    var cards = tb.cards;
    var g_this = this;
    if (tb.AllPlayerCheck(function(pl) {
                logger.debug("sendNewCard=====pl.uid:" + pl.uid);
                logger.debug("sendNewCard=====pl.mjState==TableState.waitCard:" + (pl.mjState == TableState.waitCard));
                return pl.mjState == TableState.waitCard;
            }
        )
    ) {
        // 荒庄留的牌的数量
        logger.debug("sendNewCard=====TableState.waitCard=====");
        var totalCar = this.getCardTotalNum(tData);
        logger.debug("sendNewCard=====总牌数:" + totalCar);
        logger.debug("sendNewCard=====剩余牌:" + (totalCar - tData.cardNext));
        if (tData.cardNext < totalCar) {
            logger.debug("sendNewCard=====cardNext=====" + tData.cardNext);
            var newCard = cards[tData.cardNext++];
            if (tData.putType == 0 || tData.putType == 4) {
                tData.curPlayer = (tData.curPlayer + 1) % tData.maxPlayer;
            }

            var uid = tData.uids[tData.curPlayer];
            var pl = tb.getPlayer(uid);
            pl.mjhand.push(newCard);
            pl.newSendCard = newCard;
            pl.isNew = true;
            pl.skipHu = false;
            pl.skipPeng = [];
            tData.tState = TableState.waitPut;// 发牌了,那么裁判状态为等待出牌
            tb.AllPlayerRun(function(p) {
                p.mjState = TableState.waitPut;
                p.eatFlag = 0;
            });
    
            
            if (this.getHuType(pl, 0)) {
                pl.eatFlag = 8;
            }
    
            //add by sking 明砸，必须听才可以胡牌
            var canTing = tData.areaSelectMode["tingType"]; //1 表示明砸
            if(pl.eatFlag >= 8 && canTing == 1 && !pl.isTing)
            {
                pl.eatFlag -= 8;
            }
            var flowerCard = 0;
            for (var i = 0; i < pl.mjhand.length; i++) {
                if (this.isHardFlower(pl.mjhand[i], tData)) {
                    flowerCard = pl.mjhand[i];
                }
            }
            if (flowerCard) {
                tData.roundKey = Math.random();
                if (pl.autoTimer) {
                    clearTimeout(pl.autoTimer);
                }
                pl.autoTimer = setTimeout(function() {
                    pl.autoTimer = null;
                    if (pl.mjState == TableState.waitPut) {
                        tb.MJPut(pl, {cmd: "MJPut", roundKey: tData.roundKey, card: flowerCard}, null, emptyFunction);
                    }
                }, 1000 * 0.5);
            }
            pl.notify("newCard", {newCard: newCard, eatFlag: pl.eatFlag});
            cloneDataAndPush(tb.mjlog, "logNewCard", {newCard: newCard, eatFlag: pl.eatFlag}, {uid: pl.uid});//发牌
            tb.NotifyAll("waitPut", tData);
            cloneDataAndPush(tb.mjlog, "waitPut", tData);
            tb.doTrust(pl);
            return true;
        }
        else {
            //没有牌了
            logger.debug("sendNewCard=====没牌,黄庄 endGame=====");
            this.EndGame(tb, null);
        }
    }
    else {
        logger.debug("sendNewCard=====玩家状态问题，不能发牌");
        // var notifys = [];
        // tb.AllPlayerRun(function(p) {
        //     if (p.mjState == TableState.waitCard)
        //         notifys.push(p.uid);
        // });
        // logger.debug("mjPutCard=====notifys:" + notifys);
        // tb.NotifyAll("loadOther", {uids: notifys});
        // logger.debug("mjPutCard=====已通知所有玩家等待");
    }
    return false;
};

GameCodeganyu.prototype.isHardFlower = function(card) {
    // var _flowerCount = tData.areaSelectMode["flowerCount"];
    // //花牌
    // var flowers = [71, 81, 91,111, 121, 131, 141, 151, 161, 171, 181];
    // if (_flowerCount == 36)
    // {
    //     flowers = [31, 41, 51, 61, 71, 81, 91,111, 121, 131, 141, 151, 161, 171, 181];
    // }
    // for (var i = 0; i < flowers.length; i++) {
    //     cards.push(flowers[i]);
    // }
    
    return flowerCards.indexOf(card) >= 0;
}

// 获取总的牌的数量
GameCodeganyu.prototype.getCardTotalNum = function(tData) {

    var _flowerCount = tData.areaSelectMode["flowerCount"];
    if(_flowerCount == 20)
    {
        return 128;
    }
    return 144;
}

// 获取状态
GameCodeganyu.prototype.getEatFlag = function(pl, tData) {
    logger.debug("getEatFlag===========");
    var cd = tData.lastPutCard;
    var leftCard = this.getCardTotalNum(tData) - tData.cardNext;
    var eatFlag = 0;
    if (leftCard > 0 && this.getHuType(pl, cd))//点炮
    {
        eatFlag += 8;
    }
    //if (tData.areaSelectMode["canGangPeng"])
    {
        if (leftCard > 0 && this.majiang.canGang0(pl.mjhand, cd, pl.isTing))//明杠
        {
            eatFlag += 4;
        }
        // 碰  有过碰的牌不能碰
        if (!pl.isTing && leftCard > 0 && this.majiang.canPeng(pl.mjhand, cd) && pl.skipPeng.indexOf(cd)<0)//碰
        {
            eatFlag += 2;
        }
        if (!pl.isTing && leftCard > 0 && (tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] === pl.uid) &&
            this.majiang.canChi(pl.mjhand, cd).length > 0 && tData.areaSelectMode["canChi"]) //吃
        {
            eatFlag += 1;
        }
    }
    
    return eatFlag;
}

//出
GameCodeganyu.prototype.mjPutCard = function(pl, msg, session, next, tb) {
    if (pl.isTing && msg.card != pl.newSendCard) {
        logger.debug("mjPutCard=====听牌后只能打抓的牌=" + pl.newSendCard + "你打的事=" + msg.card);
        return;
    }
    var tData = tb.tData;
    logger.debug("mjPutCard=====msg.card:" + msg.card);
    logger.debug("mjPutCard=====tData.tState:" + tData.tState);
    logger.debug("mjPutCard=====tData.uids[tData.curPlayer]:" + (tData.uids[tData.curPlayer]));
    if (tData.tState == TableState.waitPut && pl.uid == tData.uids[tData.curPlayer]) {
        var cdIdx = pl.mjhand.indexOf(msg.card);
        logger.debug("mjPutCard=====tData.tState=====" + tData.tState);
        logger.debug("mjPutCard=====pl.uid===========" + pl.uid);
        logger.debug("mjPutCard=====tData.uids=======" + tData.uids[tData.curPlayer]);
        logger.debug("mjPutCard=====msg.card=========" + msg.card);
        logger.debug("mjPutCard=====mjHand===========" + JSON.stringify(pl.mjhand));
        //正常逻辑
        if (cdIdx >= 0) {
            // 手里有这张牌,这里应该再判断是否手里是否不是14,11,8,5,2张这些张数
            tb.AllPlayerRun(function(pll) {
                pll.putType = 1;// 玩家出牌状态为普通
            });
            if (pl.autoTimer) {
                clearTimeout(pl.autoTimer);
                pl.autoTimer = null;
            }
            pl.mjhand.splice(cdIdx, 1);
            pl.skipHu = false;     //出牌了,过胡取消了
            msg.uid = pl.uid;
            tData.lastPutCard = msg.card;
            tData.putType = 0;
            tData.lastPutPlayer = tData.curPlayer;
            tData.tState = TableState.waitEat;
            pl.mjState = TableState.waitCard;
            pl.eatFlag = 0;//自己不能吃
            var eatFlags = {};
            eatFlags[pl.uid] = 0;
            var t_this = this;
            var isFlower = this.isHardFlower(msg.card);
            logger.debug("mjPutCard=====isFlower=" + isFlower);
            if (isFlower) {
                logger.debug("mjPutCard=====通知客户端补花了=======");
                pl.mjflower.push(msg.card);
                pl.putType = 5;
                tData.putType = 5;//花牌
                tb.NotifyAll("MJFlower", {uid: pl.uid, card: msg.card});
                cloneDataAndPush(tb.mjlog, "MJFlower", {uid: pl.uid, card: msg.card});
                tb.AllPlayerRun(function(pl) {
                    pl.mjState = TableState.waitCard;
                });
            }
            else {
                pl.mjput.push(msg.card);
                pl.putCount++;
                tb.AllPlayerRun(function(p) {
                    logger.debug("88888888888888888888888888888888888888888888888");
                    if (p != pl) {
                        p.eatFlag = t_this.getEatFlag(p, tData);
                        logger.debug("p.uid:888888888888888" + p.uid + "    eatFlag = " + p.eatFlag);
                        
                        
                        
                        
                        // 过胡或者选择的自摸胡
                        if (p.eatFlag >= 8 && p.skipHu) {
                            p.eatFlag -= 8;
                        }
                        
                        //add by sking 是否可吃
                        var canChi = tData.areaSelectMode["canChi"];
                        if(p.eatFlag === 1 && !canChi)//是否有吃
                        {
                            logger.debug("没有吃****");
                            p.eatFlag  = 0;
                        }
                        
                        
                        // //add by sking 听牌不点炮
                        // if(p.eatFlag >= 8 && pl.isTing)//我出牌，有人胡了（点炮），如果听牌了，不放炮
                        // {
                        //     p.eatFlag -= 8;
                        // }
    
                        //add by sking 叫听(明砸)只能自摸，不能放炮
                        var canTing = tData.areaSelectMode["tingType"];
                        if(p.eatFlag >= 8 && canTing == 1)
                        {
                            p.eatFlag -= 8;
                        }
                        
                        
                        eatFlags[p.uid] = p.eatFlag;
                        if (p.eatFlag != 0) {
                            p.mjState = TableState.waitEat;
                            tb.doTrust(p);
                        }
                        else {
                            p.mjState = TableState.waitCard;
                        }
                    }
                    else {
                        p.mjState = TableState.waitCard;
                    }
                });
                if (msg.tingAfterPut) {
                    if (this.majiang.canTing(pl.mjhand)) {
                        pl.isTing = true;
                        pl.putCardAfterTing = msg.card;
                        tb.NotifyAll("MJTing", {uid: pl.uid, putCardAfterTing: pl.putCardAfterTing});
                        cloneDataAndPush(tb.mjlog, "MJTing", {uid: pl.uid, putCardAfterTing: pl.putCardAfterTing});
                        logger.debug("MJTing=====通知客户端玩家听了==pl.uid=====" + pl.uid);
                        if (pl.putCount <= 1 && pl.mjhand.length >= 13 && pl.uid == tData.uids[tData.zhuang]) {
                            pl.isTianting = true;
                        }
                    }
                    else {
                        logger.debug("MJTing=====听牌失败了看看是不是能听======");
                    }
                }
                logger.debug("mjPutCard=====mjHand 3======" + JSON.stringify(pl.mjhand));
                var cmd = msg.cmd;
                delete msg.cmd;
                logger.debug("mjPutCard=====玩家出牌了======");
                msg.putType = tData.putType;
                msg.skipHu = pl.skipHu;
                msg.skipPeng = pl.skipPeng;
                msg.eatFlags = eatFlags;
                logger.debug("mjPutCard=====cmd=====" + cmd + "     msg:" + JSON.stringify(msg));
                tb.NotifyAll(cmd, msg);
                cloneDataAndPush(tb.mjlog, cmd, msg);
            }
            logger.debug("mjPutCard=====xcl test");
            tData.saveActions = [];
            this.sendNewCard(tb);
        }
    }
    else {
        // var notifys = [];
        // tb.AllPlayerRun(function(p) {
        //     if (p.mjState == TableState.waitCard)
        //         notifys.push(p.uid);
        // });
        // tb.NotifyAll("loadOther", {uids: notifys});
        // logger.debug("mjPutCard=====已通知所有玩家等待:" + notifys);
    }
}

GameCodeganyu.prototype.checkFinishRound = function(tb, pl) { // 判断过吃碰杠操作是否继续
    logger.debug("mjPutCard=====checkFinishRound");
    if (!tb.tData.areaSelectMode["duoHu"]) {
        return false; // 截胡玩法中“过吃碰杠”不会导致游戏结束
    }
    if (tb.CheckPlayerCount(function(p) {
            return p.mjState == TableState.roundFinish;
        }) > 0) {// 存在已经胡的玩家
        if (this.highPlayerHu(tb, pl)) { // 还有玩家未操作
            pl.mjState = TableState.roundFinish;
            pl.notify("MJPass", {mjState: pl.mjState, skipHu: pl.skipHu});
            logger.debug("mjPutCard=====checkFinishRound  还有玩家未操作");
        }
        else {
            this.EndGame(tb, pl); // 一炮多响情况下，最后操作的那个人不胡，“过吃碰杠”也应结束游戏
            logger.debug("mjPutCard=====checkFinishRound  游戏结束");
        }
        return true;
    }
    logger.debug("mjPutCard=====checkFinishRound false");
    return false;
}

//过
GameCodeganyu.prototype.mjPassCard = function(pl, msg, session, next, tb) {
    logger.debug("======================GameCodeganyu.prototype.mjPassCard======================");
    var tData = tb.tData;
    logger.debug("mjPassCard========tData.tState:" + tData.tState);
    logger.debug("mjPassCard========pl.mjState:" + pl.mjState);
    
    
    
    if (tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut) {
        cloneDataAndPush(tb.mjlog, "MJPass", {mjState: pl.mjState}, {uid: pl.uid});
    }
    else if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat) {
        if (this.checkFinishRound(tb, pl))
            return;
    
        
        logger.debug("mjPassCard========pl.eatFlag" + pl.eatFlag);
        logger.debug("mjPassCard========msg.eatFlag:" + msg.eatFlag);
        if (pl.eatFlag == msg.eatFlag) {
            logger.debug("------------mjPassCard ---");
            pl.mjState = TableState.waitCard;
            if (pl.eatFlag >= 8)// 过胡
            {
                pl.skipHu = true;
            }
            if( pl.eatFlag & 2 ){
                // 过碰
                pl.skipPeng.push(tData.lastPutCard);
            }
            pl.eatFlag = 0;
            pl.notify("MJPass", {mjState: pl.mjState, skipHu: pl.skipHu, skipPeng: pl.skipPeng});
            cloneDataAndPush(tb.mjlog, "MJPass", {mjState: pl.mjState, skipHu: pl.skipHu, skipPeng: pl.skipPeng}, {uid: pl.uid});
            tData.saveActions.sort(function (a, b) {return b.eatFlag - a.eatFlag;});
            for (var i = 0; i < tData.saveActions.length; i++) {
                var action = tData.saveActions[i];
                if (action.eatFlag <= msg.eatFlag) {
                    tData.saveActions = [];
                    action.actionFunc();
                    break;
                }
            }
            this.sendNewCard(tb);
        }
    }
    else if (tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish || tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady) {
        pl.mjState = TableState.isReady;
        tb.NotifyAll('onlinePlayer', {uid: pl.uid, onLine: true, mjState: pl.mjState});
        pl.eatFlag = 0;
        if (tb.PlayerCount() == tData.maxPlayer && tb.AllPlayerCheck(function (p) {
            return p.mjState == TableState.isReady;
        })) {
            if(tData.tState == TableState.roundFinish){
                this.countZhuang(tData, tb);
            }
            tb.runStartGame();
        }
    }
};

// 碰
GameCodeganyu.prototype.mjPengCard = function(pl, msg, session, next, tb) {
    if (pl.isTing) {
        logger.debug("mjPengCard=====pl.isTing()=====不能碰牌======" + pl.isTing);
        return;
    }
    //logger.debug("======================GameCodeganyu.prototype.mjPengCard======================");
    var tData = tb.tData;
    logger.debug("mjPengCard=====tData.tState=====" + tData.tState);
    logger.debug("mjPengCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjPengCard=====pl.uid=====" + pl.uid);
    if (
        tData.tState == TableState.waitEat
        && pl.mjState == TableState.waitEat
        && tData.uids[tData.curPlayer] != pl.uid
        && pl.skipPeng.indexOf(tData.lastPutCard) < 0// 此牌过碰的牌
    ) {
        if (this.checkFinishRound(tb, pl))
            return;
        logger.debug("mjPengCard=====11111=====");
        //此处必须保证没有其他玩家想胡牌
        if (tb.AllPlayerCheck(function(p) {
                if (p == pl) {
                    return true;
                }
                return p.eatFlag < 8;
            })) {
            logger.debug("mjPengCard=====22222=====");
            var hand = pl.mjhand;
            var matchnum = 0;
            for (var i = 0; i < hand.length; i++) {
                if (hand[i] == tData.lastPutCard) {
                    matchnum++;
                }
            }
            if (matchnum >= 2) {
                // 碰
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                hand.splice(hand.indexOf(tData.lastPutCard), 1);
                pl.mjpeng.push(tData.lastPutCard);
                pl.pengchigang["peng"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});
                pl.openDoorState = true;
                if (matchnum == 3) {
                    pl.mjpeng4.push(tData.lastPutCard);
                }
                pl.isNew = false;
                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;

                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tb.AllPlayerRun(function(p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });
                tData.tState = TableState.waitPut;
                //吃碰杠
                msg.cpginfo =
                    {
                        id: pl.uid,
                        openDoorState: pl.openDoorState,
                        pengchigang: pl.pengchigang
                    };
                tb.NotifyAll('MJPeng',
                    {
                        tData: tData,
                        from: lastPlayer,
                        cpginfo: msg.cpginfo
                    });
                cloneDataAndPush(tb.mjlog, 'MJPeng', {
                    tData: tData,
                    from: lastPlayer,
                    eatFlag: msg.eatFlag,
                    cpginfo: msg.cpginfo
                });//碰
                tb.doTrust(pl);
            }
        }
        else {
            var _this = this;
            var actionFunc = (function () {
                return function() {
                    _this.mjPengCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({eatFlag: 2, actionFunc: actionFunc});
            logger.debug("mjPengCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", {uids: [pl.uid]});
        }
    }
}

GameCodeganyu.prototype.checkChiCount = function(tb, pl, lastPutPlayer) {
    var tData = tb.tData;
    var samePl = 0;
    var chiList = pl.pengchigang["chi"].concat(pl.pengchigang["peng"]).concat(pl.pengchigang["gang"]).concat(pl.pengchigang["pgang"]);
    for (var i = 0; i < chiList.length; i++) {
        if (chiList[i].pos == lastPutPlayer)
            samePl++;
    }
    if (samePl >= 2) {
        var p = tb.players[tData.uids[lastPutPlayer]];
        p.notify("chiCount", {cUid: pl.uid, count: samePl});
        pl.notify("chiCount", {zUid: p.uid, count: samePl}); //自己吃了xx多少次
    }
}


//吃
GameCodeganyu.prototype.mjChiCard = function(pl, msg, session, next, tb) {
    logger.debug("======================mjChiCard======================");
    var tData = tb.tData;
    var canChi = tData.areaSelectMode["canChi"];
    if(!canChi)
    {
        logger.debug("================当前不可吃===========sking========");
        // tb.AllPlayerRun(function(p) {
        //     p.mjState = TableState.waitCard;
        //     p.eatFlag = 0;
        // });
        //
        return;
    }
    
    
    if (pl.isTing) {
        logger.debug("mjChiCard============pl.isTing()===不能吃牌=======" + pl.isTing);
        return;
    }
    var tData = tb.tData;
    logger.debug("mjChiCard=====tData.tState=====" + tData.tState);
    logger.debug("mjChiCard=====pl.mjState========" + pl.mjState);
    logger.debug("mjChiCard=====tData.uids[tData.curPlayer]======" + tData.uids[tData.curPlayer]);
    logger.debug("mjChiCard=====pl.uid===========" + pl.uid);
    logger.debug("mjChiCard=====tData.uids[(tData.curPlayer+1)%tData.maxPlayer]=========" + tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]);
    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat &&
        tData.uids[tData.curPlayer] != pl.uid &&
        tData.uids[(tData.curPlayer + 1) % tData.maxPlayer] == pl.uid //下家限制
    ) {
        if (this.checkFinishRound(tb, pl))
            return;
        logger.debug("==================mjChiCard====================");
        //此处必须保证没有其他玩家想 胡牌 碰牌 杠牌
        if (tb.AllPlayerCheck(function(p) {
                if (p == pl) {
                    return true;
                }
                return p.eatFlag == 0;
            })) {
            var cd0 = tData.lastPutCard;
            var cd1 = tData.lastPutCard;
            if (msg.pos == 0) {
                cd0 += 1;
                cd1 += 2;
            }
            else if (msg.pos == 1) {
                cd0 -= 1;
                cd1 += 1;
            }
            
            else {
                cd0 -= 2;
                cd1 -= 1;
            }
            
            var hand = pl.mjhand;
            var idx0 = hand.indexOf(cd0);
            var idx1 = hand.indexOf(cd1);
            logger.debug("00000000000000000idx0=====" + idx0);
            logger.debug("11111111111111111idx1=====" + idx1);
            if (idx0 >= 0 && idx1 >= 0) {
                logger.debug("mjChiCard=====idx0=====" + idx0);
                // 如果手里有这两张牌
                hand.splice(idx0, 1);
                idx1 = hand.indexOf(cd1);
                hand.splice(idx1, 1);
                var eatCards = [cd0, cd1, tData.lastPutCard];
                eatCards.sort(function(a, b) {
                    return a - b;
                });
                logger.debug("mjChiCard=====hand=====" + JSON.stringify(hand));
                logger.debug("mjChiCard=====eatCards=====" + JSON.stringify(eatCards));
                
                pl.mjchi = pl.mjchi.concat(eatCards);
                pl.mjchiCard.push(tData.lastPutCard);
                pl.isNew = false;
                
                pl.pengchigang["chi"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});
                //this.checkChiCount(tb, pl, tData.lastPutPlayer);
                
                var lastPlayer = tData.curPlayer;
                var putCardPl = tb.getPlayer(tData.uids[lastPlayer]);
                putCardPl.mjput.length = putCardPl.mjput.length - 1;
                logger.debug("mjChiCard=====putCardPl.mjput=====" + JSON.stringify(putCardPl.mjput));
                tData.curPlayer = tData.uids.indexOf(pl.uid);
                tData.tState = TableState.waitPut;
                
                tb.AllPlayerRun(function(p) {
                    p.mjState = TableState.waitPut;
                    p.eatFlag = 0;
                });
                
                //吃碰杠
                msg.cpginfo =
                    {
                        id: pl.uid,
                        pengchigang: pl.pengchigang
                    };
                
                var chiMsg =
                    {
                        mjchi: eatCards,
                        mjchiCard: pl.mjchiCard,
                        tData: JSON.parse(JSON.stringify(tData)),
                        pos: msg.pos,
                        from: lastPlayer,
                        eatFlag: msg.eatFlag,
                        cpginfo: msg.cpginfo
                    };
                logger.debug("mjChiCard=====888888888888888888888chiMsg=====" + JSON.stringify(chiMsg));
                
                tb.NotifyAll('MJChi', chiMsg);
                cloneDataAndPush(tb.mjlog, "MJChi", chiMsg);
                tb.doTrust(pl);
            }
        }
        else {
            var _this = this;
            var actionFunc = (function () {
                return function() {
                    _this.mjChiCard(pl, msg, session, next, tb);
                }
            })();
            tData.saveActions.push({eatFlag: 1, actionFunc: actionFunc});
            logger.debug("mjChiCard=====已通知玩家等待uid:" + pl.uid);
            tb.NotifyAll("loadOther", {uids: [pl.uid]});
        }
    }
};


//杠
GameCodeganyu.prototype.mjGangCard = function(pl, msg, session, next, tb) {
    var tData = tb.tData;
    logger.debug("mjGangCard=====tData.tState=====" + tData.tState);
    logger.debug("mjGangCard=====msg.card=====" + msg.card);
    logger.debug("mjGangCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjGangCard=====pl.uid=====" + pl.uid);
    if (tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid && this.checkFinishRound(tb, pl))
        return;
    if (
        (
            //吃牌杠
            tData.tState == TableState.waitEat && pl.mjState == TableState.waitEat && tData.uids[tData.curPlayer] != pl.uid
            //此处必须保证没有其他玩家想胡牌
            && (
                tb.AllPlayerCheck(function(p) {
                    if (p == pl) return true;
                    return p.eatFlag < 8;
                })
            )
            //自摸牌杠
            || tData.tState == TableState.waitPut && pl.mjState == TableState.waitPut && tData.uids[tData.curPlayer] == pl.uid
        )
    ) {
        logger.debug("mjGangCard=====11111=====");
        var hand = pl.mjhand;
        var handNum = 0;
        var putCardPl = null;
        for (var i = 0; i < hand.length; i++) {
            if (hand[i] == msg.card) {
                handNum++;
            }
        }
        if (tData.tState == TableState.waitEat && handNum == 3 && tData.lastPutCard == msg.card) {
            logger.debug("mjGangCard=====明杠=====11111=====");
            // 自己有三张，杠别人的
            putCardPl = tb.getPlayer(tData.uids[tData.curPlayer]);
            var mjput = putCardPl.mjput;
            if (mjput.length > 0 && mjput[mjput.length - 1] == msg.card) {
                mjput.length = mjput.length - 1;
            }
            else {
                return;
            }
            logger.debug("mjGangCard=====明杠=====22222=====");
            //记录点杠
            pl.mjgang0.push(msg.card);//吃明杠
            //结算界面明杠数+1
            pl.minggangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            msg.gang = 1;
            msg.from = tData.curPlayer;
            pl.isNew = false;
            pl.pengchigang["gang"].push({pos: tData.lastPutPlayer, card: tData.lastPutCard});
            logger.debug("mjGangCard=====明杠=====33333=====");
        }
        else if (tData.tState == TableState.waitPut && handNum == 4) {
            logger.debug("mjGangCard=====暗杠=====11111=====");
            // 暗杠
            pl.mjgang1.push(msg.card);
            //结算界面暗杠数+1
            pl.angangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            hand.splice(hand.indexOf(msg.card), 1);
            msg.gang = 3;
        }
        else if (tData.tState == TableState.waitPut && handNum == 1 && pl.mjpeng.indexOf(msg.card) >= 0) {
            //自摸明杠
            logger.debug("mjGangCard=====自摸明杠=====11111=====");
            //找出点碰的人
            var pengPos = tData.lastPutPlayer;
            var pengList = pl.pengchigang["peng"];
            for (var t = 0; t < pengList.length; t++) {
                var item = pengList[t];
                if (item.card == msg.card) {
                    pengPos = item.pos;
                    pengList.splice(t, 1);
                }
            }
            pl.pengchigang["pgang"].push({pos: pengPos, card: msg.card});
            pl.mjgang0.push(msg.card);
            //结算界面明杠数+1
            pl.minggangTotal += 1;
            hand.splice(hand.indexOf(msg.card), 1);
            pl.mjpeng.splice(pl.mjpeng.indexOf(msg.card), 1);
            msg.gang = 2;
        }
        else {
            return;
        }
        msg.uid = pl.uid;
        
        //判断是否有抢杠胡
        var canRobGangHu = 0;
        pl.putType = 4;
        var t_this = this;
        // logger.debug("tData.playType:"+tData.playType);
        var eatFlags = {};
        for (var i = 0; i < tData.maxPlayer; i++) {
            var p = tb.players[tData.uids[(tData.curPlayer + i) % tData.maxPlayer]];
            // 抢杠
            p.mjState = TableState.waitCard;
            p.eatFlag = 0;
            
            if (msg.gang == 2 && p != pl && !p.skipHu && tData.areaSelectMode["tingType"] != 1) {
                // 只有在自摸明杠的时候才能抢杠胡
                
                //选择点炮胡才能抢杠
                var huState = t_this.getHuType(p, msg.card);
                if (huState && (tData.areaSelectMode["duoHu"] || canRobGangHu == 0))//抢杠胡
                {
                    canRobGangHu++;
                    p.mjState = TableState.waitEat;
                    p.eatFlag = 8;
                    tb.doTrust(p);
                }
            }
            eatFlags[p.uid] = p.eatFlag;
            logger.debug("mjGangCard=====p.mjState:" + p.mjState);
        };
        tData.tState = TableState.waitEat;
        
        
        

        
        
        if (canRobGangHu > 0 ) {
            logger.debug("明砸不能抢杠胡");
            // 抢杠胡
            tData.putType = 2;
            tData.curPlayer = tData.uids.indexOf(pl.uid);
            tData.lastPutCard = msg.card;
        }
        else {
            tData.putType = 0;
            tData.curPlayer = (tData.uids.indexOf(pl.uid) + (tData.maxPlayer - 1)) % tData.maxPlayer;
        }
        
        //吃碰杠
        msg.cpginfo =
            {
                id: pl.uid,
                pengchigang: pl.pengchigang,
                openDoorState: pl.openDoorState
            };
        msg.eatFlags = eatFlags;
        //杠后当前状态为杠
        tb.NotifyAll('MJGang', msg);
        cloneDataAndPush(tb.mjlog, 'MJGang', msg);
        logger.debug("mjGangCard=====通知客户端扛了=====");
        if (tData.putType != 2) {
            // 不被别人抢杠，才会发牌
            this.sendNewCard(tb);
        }
    }
    else {
        var _this = this;
        var actionFunc = (function () {
            return function() {
                _this.mjGangCard(pl, msg, session, next, tb);
            }
        })();
        tData.saveActions.push({eatFlag: 4, actionFunc: actionFunc});
        logger.debug("mjGangCard=====已通知玩家等待uid:" + pl.uid);
        tb.NotifyAll("loadOther", {uids: [pl.uid]});
    }
}

GameCodeganyu.prototype.highPlayerHu = function(tb, pl) {
    var g_this = this;
    if (tb.CheckPlayerCount(function(p) {
            logger.debug("highPlayerHu=====p.mjState:" + p.mjState + ", eatFlag = " + p.eatFlag);
            if (pl.uid != p.uid && p.mjState == TableState.waitEat && p.eatFlag >= 8) {
                return true;
            }
            return false;
        }) > 0) {
        logger.debug("highPlayerHu=====HighPlayerHu: return true=====");
        return true;
    }
    logger.debug("highPlayerHu=====HighPlayerHu: return false=====");
    return false;
}

GameCodeganyu.prototype.mjJieHu = function(pl, tb) {
    var tData = tb.tData;
    if (pl === tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]]) {

    }
    else if (pl === tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8) {
            return true;
        }
    }
    else if (pl === tb.players[tData.uids[(tData.curPlayer + 3) % tData.maxPlayer]]) {
        var eatFlag1 = tb.players[tData.uids[(tData.curPlayer + 1) % tData.maxPlayer]].eatFlag;
        var eatFlag2 = tb.players[tData.uids[(tData.curPlayer + 2) % tData.maxPlayer]].eatFlag;
        if (eatFlag1 >= pl.eatFlag || eatFlag1 >= 8 || eatFlag2 >= pl.eatFlag || eatFlag2 >= 8) {
            return true;
        }
    }
    return false;
}

// 胡
GameCodeganyu.prototype.mjHuCard = function(pl, msg, session, next, tb) {
    logger.debug("======================mjHuCard======================");
    var tData = tb.tData;
    var canEnd = false;
    logger.debug("mjHuCard=====tData.tState=====" + tData.tState);
    logger.debug("mjHuCard=====pl.mjState=====" + pl.mjState);
    logger.debug("mjHuCard=====pl.isNew=====" + pl.isNew);
    logger.debug("mjHuCard=====tData.uids[tData.curPlayer]=====" + tData.uids[tData.curPlayer]);
    logger.debug("mjHuCard=====pl.mjhand=====" + pl.mjhand);
    //自摸胡
    if (
        tData.tState === TableState.waitPut &&
        pl.mjState === TableState.waitPut &&
        pl.isNew &&
        tData.uids[tData.curPlayer] === pl.uid &&
        pl.eatFlag >= 8
    ) {
        logger.debug("mjHuCard=====自摸=====");
        pl.winType = WinType.pickNormal;    //自摸
        //结算自摸胡加1
        pl.zimoTotal += 1;
        canEnd = true;
        tData.lastPutCard = pl.newSendCard;
    }
    else if (
        tData.tState === TableState.waitEat &&
        pl.mjState === TableState.waitEat &&
        tData.uids[tData.curPlayer] !== pl.uid &&
        pl.eatFlag >= 8 && !pl.skipHu
    ) {
        if (tData.tState === TableState.waitEat) {
            logger.debug("mjHuCard=====点炮胡=====");
            //截胡
            var duoHu = tData.areaSelectMode["duoHu"];
            if (!duoHu && this.mjJieHu(pl, tb)) {
                logger.debug("mjHuCard=====有截胡玩家，请等待=======");
                logger.debug("mjHuCard=====通知客户端等待其他玩家操作");
                logger.debug("mjHuCard=====已通知玩家等待uid:" + pl.uid);
                tb.NotifyAll("loadOther", {uids: [pl.uid]});
                return;
            }
            var winType = null;
            if (tData.putType === 0 || tData.putType == 4) //点炮
            {
                winType = WinType.eatPut;
                pl.dianPaoPlayer = tData.lastPutPlayer;
                //结算界面点炮总数+1
                var dianPaoPl = tb.getPlayer(tData.uids[tData.lastPutPlayer]);
                dianPaoPl.dianpaoTotal += 1;
            }
            else //抢杠胡tData.putType == 2
            {
                winType = WinType.eatGang;
                pl.dianPaoPlayer = tData.curPlayer;
                //结算界面点炮总数+1
                var robGangPl = tb.getPlayer(tData.uids[tData.curPlayer]);
                robGangPl.dianpaoTotal += 1;
                logger.debug("mjHuCard=====删除杠牌" + tData.lastPutCard);
                tb.AllPlayerRun(function(p) {
                    var pgangList = p.pengchigang["pgang"];
                    logger.debug("mjHuCard=====" + p.uid, pgangList);
                    for (var i = 0; i < pgangList.length; i++) {
                        if (pgangList[i].card == tData.lastPutCard) {
                            logger.debug("mjHuCard=====splice");
                            p.pengchigang["peng"].push(pgangList[i]);
                            pgangList.splice(i, 1);
                        }
                    }
                    var gangIndex = p.mjgang0.indexOf(tData.lastPutCard);
                    if (gangIndex >= 0) {
                        logger.debug("mjHuCard=====mjgang0");
                        p.mjpeng.push(tData.lastPutCard)
                        p.mjgang0.splice(gangIndex, 1);
                    }
                });
                logger.debug("mjHuCard=====删除杠牌 end");
            }
            logger.debug("mjHuCard=====pl.uid:" + pl.uid);
            pl.mjhand.push(tData.lastPutCard);
            pl.winType = winType;
            //结算界面接炮总数+1
            pl.jiepaoTotal += 1;
            logger.debug("mjHuCard=====pl.mjState==TableState.waitEat=====" + (pl.mjState == TableState.waitEat));
            if (duoHu && pl.mjState == TableState.waitEat && this.highPlayerHu(tb, pl)) {
                logger.debug("mjHuCard=====一炮多胡=====");
                logger.debug("mjHuCard=====roundFinish: pl.uid=====" + pl.uid);
                tb.NotifyAll("loadOther", {uids: [pl.uid]});
                pl.mjState = TableState.roundFinish;
                var huMsg = {
                    uid: pl.uid, 
                    eatFlag: msg.eatFlag,
                    mjhand: pl.mjhand,
                    huWord: this.majiang.showHuWord(tData, pl)
                };
                tb.NotifyAll("MJHu", huMsg);
                cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
            }
            else {
                logger.debug("mjHuCard=====截胡=====");
                canEnd = true;
            }
        }
    }
    if (canEnd) {
        logger.debug("mjHuCard=====canEnd=====");
        var huMsg = {
            uid: pl.uid, 
            eatFlag: msg.eatFlag,
            mjhand: pl.mjhand,
            huWord: this.majiang.showHuWord(tData, pl)
        };
        tb.NotifyAll("MJHu", huMsg);
        cloneDataAndPush(tb.mjlog, "MJHu", huMsg);
        this.EndGame(tb, pl);
    }
}

GameCodeganyu.prototype.EndRoom = function(tb, msg) {
    var playInfo = null;
    if (tb.tData.roundNum > -2) {
        if (tb.tData.roundNum != tb.createParams.round) {
            var tData = tb.tData;
            playInfo = {
                gametype: tData.gameType,
                owner: tData.owner,
                money: tb.createParams.money,
                now: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                tableid: tb.tableid,
                players: []
            };
            tb.AllPlayerRun(function(p) {
                var pinfo = {};
                pinfo.uid = p.uid;
                pinfo.winall = p.winall;
                pinfo.nickname = p.info.nickname;
                pinfo.money = p.info.money;
                playInfo.players.push(pinfo);
                p.info.lastGameTime = new Date().getTime();
                modules.user.updateInfo({userId: p.uid, lastGameTime: p.info.lastGameTime});
            });
        }
        if (msg) {
            if (playInfo) msg.playInfo = playInfo;
            if(!msg.showEnd) msg.showEnd = tb.tData.roundNum != tb.createParams.round;
            msg.players = tb.collectPlayer('lastOffLineTime');
            msg.serverTime = Date.now();
            tb.NotifyAll("endRoom", msg);
        }
        tb.validTableEndDo();
        tb.SetTimer();
        tb.tData.roundNum = -2;
        this.DestroyTable(tb);
        tb.endVipTable(tb.tData);
    }
    return playInfo;
};

GameCodeganyu.prototype.huScore = function(tb, pl) {
    //算分
    var tData = tb.tData;
    tData.winner = tData.uids.indexOf(pl.uid);
    logger.debug("tData.winner:" + tData.winner);
    var cd = pl.mjhand[pl.mjhand.length - 1];
    
    var mjhandSorted = pl.mjhand.slice();
    mjhandSorted.sort(numCmp);
    var mjhandBeforeHu = pl.mjhand.slice(0, -1);
    mjhandBeforeHu.sort(numCmp);
    var isDuiDuiHu = this.majiang.isDuiDuiHu(pl.mjchi, mjhandSorted);
    var is7Dui = this.majiang.is7Dui(mjhandSorted);
    var score = tb.tData.areaSelectMode["difen"];
    var bShowDiFen = false;
    
    
    
    
    /*
        牌型
     */
    if (pl.winType == WinType.pickNormal) {
        pl.mjdesc.push("自摸");
    }
    
    //7对
    pl.mjdesc.push("底分" + score);
    if (is7Dui) {
        bShowDiFen = true;
        score *= 2;
        pl.mjdesc.push("七对×2");
    }

    //清一色
    if (this.majiang.isSameColor(pl.mjhand, pl.mjchi, pl.mjpeng, pl.mjgang0, pl.mjgang1)) {
        bShowDiFen = true;
        score *= 2;
        pl.mjdesc.push("清一色×2");
    }

    //杠后花
    if (pl.putType === 4) {
        bShowDiFen = true;
        score *= 2;
        pl.mjdesc.push("杠上开花×2");
    }

    if(!bShowDiFen)
    {
        pl.mjdesc.push("底分+" + score);
    }
    
    logger.debug("huScore=====杠分=====");
    if (pl.mjgang1.length > 0) {
        score += pl.mjgang1.length * 2;
        pl.mjdesc.push("暗杠+" + (pl.mjgang1.length * 2));
    }
    if (pl.pengchigang["gang"].length > 0) {
        score += pl.pengchigang["gang"].length * 1;
        pl.mjdesc.push("明杠+" + (pl.pengchigang["gang"].length * 1));
    }
    if (pl.pengchigang["pgang"].length > 0) {
        score += pl.pengchigang["pgang"].length * 1;
        pl.mjdesc.push("补杠+" + (pl.pengchigang["pgang"].length * 1));
    }

    
    if (pl.winType === WinType.pickNormal)  //自摸，只听一张牌，分数翻倍
    {
        var canTing = tData.areaSelectMode["tingType"];
        var tingCards = this.majiang.calTingSet(pl.mjhand);
        logger.debug("tingCards==========" + Object.keys(tingCards).length );
        logger.debug("canTing==========" + canTing);
        if(Object.keys(tingCards).length == 1 && canTing == 1)
        {
            score *= 2;
            pl.mjdesc.push("听单张×2");
        }
    }
    
    logger.debug("huScore=====算花=====");
    if (pl.mjflower.length > 0) {
        score += pl.mjflower.length * 1;
        pl.mjdesc.push("花牌+" + (pl.mjflower.length * 1));
    }
    
    
    function payScore(p) {
        var payScore = score;
        p.winone -= payScore;
        pl.winone += payScore;
    }
    
    if (pl.winType === WinType.pickNormal) { //自摸
        tb.AllPlayerRun(function(p) {
            if (p != pl) {
                payScore(p);
            }
        });
    }
    else { //点炮
        var dianPaoId = tData.uids[pl.dianPaoPlayer];
        var p = tb.getPlayer(dianPaoId);
        p.mjdesc.push("点炮");
        payScore(p);
    }
    
    logger.debug("huScore=====结算结束=====");
};

GameCodeganyu.prototype.EndGame = function(tb, pl, byEndRoom) {
    logger.debug("========GameCodeganyu.prototype.endGame========");
    var tData = tb.tData;
    var this_t = this;
    tb.AllPlayerRun(function(p) {
        p.mjState = TableState.roundFinish;
        p.mjdesc = p.mjdesc || [];
    });
    if (byEndRoom) {
        tb.showDissmissDesc();
    }
    //算分
    if (pl) {
        tb.AllPlayerRun(function(p) {
            if (p.winType > 0) {
                this_t.huScore(tb, p);
            }
        });
    }
    //流局
    else {
        tData.winner = -1;
    }
    tData.tState = TableState.roundFinish;
    if (tData.roundAll == tData.roundNum) {
        tb.firstRoundEndDo();
    }
    
    tb.AllPlayerRun(function(p) {
        p.winall += p.winone;
        // if (!p.info.$inc) {
        //     p.info.$inc = {playNum: 1}; // +playNum
        // } else if (!p.info.$inc.playNum) {
        //     p.info.$inc.playNum = 1;
        // } else {
        //     p.info.$inc.playNum += 1;
        // }
    });
    tb.perRoundEndDo();
    tData.roundNum--;
    var roundEnd = {
        players: tb.collectPlayer('mjhand', 'mjdesc', 'winone', 'winall', 'winType', 'baseWin', 'jiepaoTotal','zimoTotal', 'dianpaoTotal', 'minggangTotal', 'angangTotal', 'mjpeng', 'mjgang0', 'mjflower', 'info' ,'lastOffLineTime'),
        tData: tData,
        roundEndTime: utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        isDismiss: !!byEndRoom
    };

    cloneDataAndPush(tb.mjlog, "roundEnd", roundEnd);//一局结束
    var playInfo = null;
    if (tData.roundNum == 0)
        playInfo = this.EndRoom(tb);//结束
    if (playInfo) roundEnd.playInfo = playInfo;
    logger.debug("EndGame============服务器告诉客户端一局完事了============");
    tb.NotifyAll("roundEnd", roundEnd);
}

GameCodeganyu.prototype.DestroyTable = function(tb) {
    if (tb.PlayerCount() == 0 && tb.tData.roundNum == -2) {
        tb.tData.roundNum = -3;
        tb.Destroy();
    }
}

GameCodeganyu.prototype.countZhuang = function(tData, tb) {
    logger.debug("===================GameCodeganyu.prototype.countZhuang==================");
    logger.debug("tData.zhuang:" + tData.zhuang);
    logger.debug("tData.winner:" + tData.winner);
    if (tData.zhuang == -1) {
        //第一局
        tData.curPlayer = 0;
    }
    else if (tData.winner == -1) {
        tData.curPlayer = tData.zhuang;
    }
    else {
        tData.curPlayer = tData.winner;
    }
    tData.zhuang = tData.curPlayer;
};

module.exports = GameCodeganyu;